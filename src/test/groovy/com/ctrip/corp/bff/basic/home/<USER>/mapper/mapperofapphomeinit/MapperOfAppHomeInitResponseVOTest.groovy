package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofapphomeinit

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType
import com.ctrip.corp.bff.basic.contract.MenuUnitInfoVO
import com.ctrip.corp.bff.basic.contract.SupportVO
import com.ctrip.corp.bff.basic.contract.UserInfoVO
import com.ctrip.corp.bff.basic.home.contract.BrandInfo
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType
import com.ctrip.corp.bff.basic.home.contract.ProductInfo
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.contract.LogoInfoVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.apphome.AppHomeConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.apphome.AppHomeInfo
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8
import com.ctrip.corp.bff.im.contract.CountInfo
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification

class MapperOfAppHomeInitResponseVOTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        BrandInfoInitResponseType brandInfoInitResponseType = new BrandInfoInitResponseType()
        ProductInfoInitResponseType productInfoInitResponseType = new ProductInfoInitResponseType()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = new GetCorpUserInfoResponseType()
        AppHomeConfig appHomeConfig = new AppHomeConfig()
        appHomeConfig.setProductTabList(Lists.newArrayList())
        appHomeConfig.setHomeButtonList(Lists.newArrayList())
        TemplateSoaRequestType templateSoaRequestType = new TemplateSoaRequestType()
        CountInfoQueryResponseType countInfoQueryResponseType = new CountInfoQueryResponseType()
        when:
        AppHomeInitResponseVO result = mapper.convert(Tuple8.of(brandInfoInitResponseType, productInfoInitResponseType,
                customGrayResponseType, queryApproveTaskResponseType, getCorpUserInfoResponseType, appHomeConfig,
                templateSoaRequestType, countInfoQueryResponseType))
        appHomeConfig.setBackgroundUrlMap(null)
        appHomeConfig.getBackgroundUrlMap()
        then:
        true
    }

    def "testBuildMenuInfoList"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        List<AppHomeInfo> appHomeInfoList = Lists.newArrayList()
        AppHomeInfo appHomeInfo = new AppHomeInfo()
        appHomeInfo.setCode("1")
        appHomeInfoList.add(appHomeInfo)
        Map<String, MenuUnitInfoVO> menuUnitInfoMap = Maps.newHashMap()
        menuUnitInfoMap.put("1", new MenuUnitInfoVO())
        when:
        List<MenuUnitInfoVO> result = mapper.buildMenuInfoList(appHomeInfoList, menuUnitInfoMap)
        appHomeInfo.getCode()
        appHomeInfo.getIcon()
        appHomeInfo.setIcon("1")
        appHomeInfo.getUrl()
        appHomeInfo.setUrl("1")
        appHomeInfo.getUnreadMessageIcon()
        appHomeInfo.setUnreadMessageIcon("1")
        appHomeInfo.getSort()
        appHomeInfo.setSort(1)
        appHomeInfo.getPublicBackgroundUrl()
        appHomeInfo.setPublicBackgroundUrl("1")
        appHomeInfo.getPrivateBackGroundUrl()
        appHomeInfo.setPrivateBackGroundUrl("1")
        then:
        true
    }

    def "testBuildProductTabList1"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        ProductInfoInitResponseType productInfoInitResponseType = new ProductInfoInitResponseType()
        List<AppHomeInfo> appHomeInfoList = Lists.newArrayList()
        when:
        List<MenuUnitInfoVO> result = mapper.buildProductTabList(null, appHomeInfoList)
        then:
        true
    }

    def "testBuildProductTabList2"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        ProductInfoInitResponseType productInfoInitResponseType = new ProductInfoInitResponseType()
        ProductInfo productInfo = new ProductInfo()
        productInfo.setShow("F")
        ProductInfo productInfo1 = new ProductInfo()
        productInfo1.setShow("T")
        productInfoInitResponseType.setProductInfos(Lists.newArrayList(productInfo, productInfo1, null))
        List<AppHomeInfo> appHomeInfoList = Lists.newArrayList()
        when:
        List<MenuUnitInfoVO> result = mapper.buildProductTabList(productInfoInitResponseType, appHomeInfoList)
        then:
        true
    }

    def "testBuildLogoInfoVO1"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        BrandInfoInitResponseType brandInfoInitResponseType = new BrandInfoInitResponseType()
        AppHomeConfig appHomeConfig = new AppHomeConfig()
        PosEnum posEnum = PosEnum.TAIWAN
        when:
        LogoInfoVO result = mapper.buildLogoInfoVO(null, appHomeConfig, posEnum)
        then:
        true
    }

    def "testBuildLogoInfoVO2"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        BrandInfoInitResponseType brandInfoInitResponseType = new BrandInfoInitResponseType()
        brandInfoInitResponseType.setFooterLogo("1")
        AppHomeConfig appHomeConfig = new AppHomeConfig()
        PosEnum posEnum = PosEnum.TAIWAN
        when:
        LogoInfoVO result = mapper.buildLogoInfoVO(brandInfoInitResponseType, appHomeConfig, posEnum)
        then:
        true
    }

    def "testBuildLogoInfoVO3"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        BrandInfoInitResponseType brandInfoInitResponseType = new BrandInfoInitResponseType()
        brandInfoInitResponseType.setFooterLogo("")
        BrandInfo brandInfo = new BrandInfo()
        brandInfoInitResponseType.setBrandInfo(brandInfo)
        AppHomeConfig appHomeConfig = new AppHomeConfig()
        PosEnum posEnum = PosEnum.TAIWAN
        when:
        LogoInfoVO result = mapper.buildLogoInfoVO(brandInfoInitResponseType, appHomeConfig, posEnum)
        then:
        true
    }

    def "testBuildUserInfo"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        when:
        UserInfoVO result = mapper.buildUserInfo(null)
        then:
        true
    }

    def "testCheck"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

    def "testGetNotApprovalNum1"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        CountInfoQueryResponseType countInfoQueryResponseType = new CountInfoQueryResponseType()
        CountInfo countInfo = new CountInfo()
        countInfoQueryResponseType.setCountInfos(Lists.newArrayList(countInfo, null))
        when:
        String result = mapper.getNotApprovalNum(countInfoQueryResponseType)
        then:
        result == "0"
    }

    def "testGetNotApprovalNum2"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        CountInfoQueryResponseType countInfoQueryResponseType = new CountInfoQueryResponseType()
        CountInfo countInfo = new CountInfo()
        countInfo.setKey(CommonConstant.NOT_APPROVAL_NUM)
        countInfo.setCount(1)
        countInfoQueryResponseType.setCountInfos(Lists.newArrayList(countInfo))
        when:
        String result = mapper.getNotApprovalNum(countInfoQueryResponseType)
        then:
        result == "1"
    }

    def "testBuildMenuInfoList1"() {
        given:
        MapperOfAppHomeInitResponseVO mapper = new MapperOfAppHomeInitResponseVO()
        AppHomeInfo appHomeInfo = new AppHomeInfo()
        appHomeInfo.setCode("1")
        List<AppHomeInfo> appHomeInfoList = Lists.newArrayList(appHomeInfo)
        Map<String, MenuUnitInfoVO> map = Maps.newHashMap()
        MenuUnitInfoVO menuUnitInfoVO = new MenuUnitInfoVO()
        SupportVO supportVO1 = new SupportVO()
        supportVO1.setCode(CommonConstant.SUPPORT_PUBLIC)
        SupportVO supportVO2 = new SupportVO()
        supportVO2.setCode(CommonConstant.SUPPORT_PRIVATE)
        SupportVO supportVO3 = new SupportVO()
        supportVO3.setCode("1")
        menuUnitInfoVO.setSupports(Lists.newArrayList(supportVO1, supportVO2, supportVO3))
        map.put("1", menuUnitInfoVO)
        when:
        List<MenuUnitInfoVO> result = mapper.buildMenuInfoList(appHomeInfoList, map)
        then:
        true
    }

}
