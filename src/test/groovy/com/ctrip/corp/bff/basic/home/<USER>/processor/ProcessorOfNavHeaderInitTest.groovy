package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice.HandlerOfCheckAuth
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice.HandlerOfQueryAuth
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfImTripInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffmcinfoserviceclient.HandlerOfMyProfileMenuInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpsettlementsettingsuncoresvcapplicationclient.HandlerOfCheckCustomerCbkPermission
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCheckAuthRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCheckCustomerCbkPermissionRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfGetCorpUserInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfImTripInitRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfMyProfileMenuInitRequestType
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfNavHeaderInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfProductInfoInitRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfQueryAuthRequest
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/1/22 10:19
 * @Version 1.0
 */
class ProcessorOfNavHeaderInitTest extends Specification {

    MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest = Mock()
    HandlerOfGetCorpInfo handlerOfGetCorpInfo = Mock()
    MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest = Mock()
    HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo = Mock()
    MapperOfNavHeaderInitResponseVO mapperOfNavHeaderInitResponseVO = Mock()
    MapperOfQueryAuthRequest mapperOfQueryAuthRequest = Mock()
    HandlerOfQueryAuth handlerOfQueryAuth = Mock()
    MapperOfCheckAuthRequest mapperOfCheckAuthRequest = Mock()
    HandlerOfCheckAuth handlerOfCheckAuth = Mock()
    MapperofSoaGeneralSearchAccountInfoRequest mapperofSoaGeneralSearchAccountInfoRequest = Mock()
    HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo = Mock()
    MapperOfCustomGrayRequest mapperOfCustomGrayRequest = Mock()
    HandlerOfCustomGray handlerOfCustomGray = Mock()
    MapperOfProductInfoInitRequest mapperOfProductInfoInitRequest = Mock()
    HandlerOfProductInfoInit handlerOfProductInfoInit = Mock()
    MapperOfCheckCustomerCbkPermissionRequest mapperOfCheckCustomerCbkPermissionRequest = Mock()
    HandlerOfCheckCustomerCbkPermission handlerOfCheckCustomerCbkPermission = Mock()
    HandlerOfBrandInfoInit handlerOfBrandInfoInit = Mock()
    MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest = Mock()
    MapperOfMyProfileMenuInitRequestType mapperOfMyProfileMenuInitRequestType = Mock()
    HandlerOfMyProfileMenuInit handlerOfMyProfileMenuInit = Mock()
    MapperOfImTripInitRequest mapperOfImTripInitRequest = Mock()
    HandlerOfImTripInit handlerOfImTripInit = Mock()

    def processor = new ProcessorOfNavHeaderInit(
            mapperOfSoaGetCorpInfoRequest: mapperOfSoaGetCorpInfoRequest,
            handlerOfGetCorpInfo: handlerOfGetCorpInfo,
            mapperOfGetCorpUserInfoRequest: mapperOfGetCorpUserInfoRequest,
            handlerOfGetCorpUserInfo: handlerOfGetCorpUserInfo,
            mapperOfNavHeaderInitResponseVO: mapperOfNavHeaderInitResponseVO,
            mapperOfQueryAuthRequest: mapperOfQueryAuthRequest,
            handlerOfQueryAuth: handlerOfQueryAuth,
            mapperOfCheckAuthRequest: mapperOfCheckAuthRequest,
            handlerOfCheckAuth: handlerOfCheckAuth,
            mapperofSoaGeneralSearchAccountInfoRequest: mapperofSoaGeneralSearchAccountInfoRequest,
            handlerOfGeneralSearchAccountInfo: handlerOfGeneralSearchAccountInfo,
            mapperOfCustomGrayRequest: mapperOfCustomGrayRequest,
            handlerOfCustomGray: handlerOfCustomGray,
            mapperOfProductInfoInitRequest: mapperOfProductInfoInitRequest,
            handlerOfProductInfoInit: handlerOfProductInfoInit,
            mapperOfCheckCustomerCbkPermissionRequest: mapperOfCheckCustomerCbkPermissionRequest,
            handlerOfCheckCustomerCbkPermission: handlerOfCheckCustomerCbkPermission,
            handlerOfBrandInfoInit: handlerOfBrandInfoInit,
            mapperOfBrandInfoInitRequest: mapperOfBrandInfoInitRequest,
            mapperOfMyProfileMenuInitRequestType: mapperOfMyProfileMenuInitRequestType,
            handlerOfMyProfileMenuInit: handlerOfMyProfileMenuInit,
            mapperOfImTripInitRequest: mapperOfImTripInitRequest,
            handlerOfImTripInit: handlerOfImTripInit
    )

    def "Execute"() {
        when:
        mapperOfNavHeaderInitResponseVO.map(Tuple1.of(_)) >> new NavHeaderInitResponseVO()
        def result = processor.execute(null)
        then:
        result == null
    }
}
