package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit

import com.ctrip.corp.bff.basic.contract.TabBarVO
import com.ctrip.corp.bff.basic.home.contract.SideBarInfo
import com.ctrip.corp.bff.basic.home.contract.SideBarInitResponseType
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitResponseVO
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.google.common.collect.Lists
import spock.lang.Specification

class MapperOfSideBarInitResponseVOTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfSideBarInitResponseVO mapper = new MapperOfSideBarInitResponseVO()
        SideBarInitResponseType responseType = new SideBarInitResponseType()
        when:
        SideBarInitResponseVO result = mapper.convert(Tuple1.of(responseType))
        then:
        true
    }

    def "testCheck"() {
        given:
        MapperOfSideBarInitResponseVO mapper = new MapperOfSideBarInitResponseVO()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

    def "testBuildSideBarList"() {
        given:
        MapperOfSideBarInitResponseVO mapper = new MapperOfSideBarInitResponseVO()
        SideBarInfo sideBarInfo = new SideBarInfo()
        when:
        List<TabBarVO> result = mapper.buildSideBarList(Lists.newArrayList(sideBarInfo))
        then:
        true
    }

}
