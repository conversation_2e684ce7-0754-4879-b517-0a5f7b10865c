package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit

import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitRequestVO
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import spock.lang.*


class MapperOfBrandInfoInitRequestTest extends Specification {
    MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest = new MapperOfBrandInfoInitRequest
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        BrandInfoInitRequestType result = mapperOfBrandInfoInitRequest.convert(Tuple2.of(
               new TemplateSoaRequestType(sourceFrom: SourceFrom.H5), "a"))

        then:
        result != null
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfBrandInfoInitRequest.check(null)

        then:
        result == null
    }
}