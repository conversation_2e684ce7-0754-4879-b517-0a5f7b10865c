package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflegaldocumentquery

import com.ctrip.corp.bff.basic.home.trip.common.enums.DocumentProductEnum
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigItem
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/4/9 16:53
 * @description:
 */
class MapperOfLegalDocumentResponseTest extends Specification {

    MapperOfLegalDocumentResponse mapper = new MapperOfLegalDocumentResponse()

    LegalDocumentConfig legalDocumentConfig = QConfigUtil.getFile("LegalDocumentConfig.json").asJson(LegalDocumentConfig.class)

    def setup() {
        MockitoAnnotations.openMocks(this)

    }

    def testFilterLegalDocumentConfigItem() {
        expect:
        def result = mapper.filterLegalDocumentConfigItem(new LegalDocumentQueryRequestVO(new TemplateSoaRequestType(gatewayHost: gatewayhost)), legalDocumentConfig.getLegalDocumentList(), corpInfo, countryCode)
        LegalDocumentConfigItem termsAndConditionsConfig =
                mapper.getConfigByEnum(result, DocumentProductEnum.TERMS_AND_CONDITIONS)
        (termsAndConditionsConfig == null ? null : termsAndConditionsConfig.getPdfUrl()) == res
        HostUtil.isTripDeskHost(gatewayhost) == isDeskHost
        where:
        gatewayhost                       | corpInfo | countryCode || res                                                               | isDeskHost
        "desk.biz.trip.fat4.tripqate.com" | null     | ""          || "https://docs.c-ctrip.com/files/6/corp/0J84u12000eqnqvym17B6.pdf" | true
        ""                                | null     | ""          || null                                                              | false
    }

}