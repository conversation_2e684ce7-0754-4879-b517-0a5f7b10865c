package com.ctrip.corp.bff.basic.home.trip.common.mapper

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

class MapperOfQueryApproveTaskRequestTypeTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfQueryApproveTaskRequestType mapper = new MapperOfQueryApproveTaskRequestType()
        when:
        QueryApproveTaskRequestType result = mapper.convert(Tuple1.of(new TemplateSoaRequestType()))
        then:
        result != null
    }

    def "testCheck"() {
        given:
        MapperOfQueryApproveTaskRequestType mapper = new MapperOfQueryApproveTaskRequestType()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

}