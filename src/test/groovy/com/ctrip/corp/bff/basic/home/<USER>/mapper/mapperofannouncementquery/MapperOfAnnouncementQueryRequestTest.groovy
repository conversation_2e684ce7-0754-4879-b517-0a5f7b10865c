package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery

import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.CommonBaseEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import spock.lang.Specification

class MapperOfAnnouncementQueryRequestTest extends Specification {

    def mapper = new MapperOfAnnouncementQueryRequest()


    def "test buildCityInfos"() {
        def resp = new GetCityBaseInfoResponseType()

        resp.setCityBaseInfo(Arrays.asList(new CityBaseInfoEntity(countryInfo: new CommonBaseEntity(id: 123))))
        expect:
        mapper.buildCityInfos(resp) != null
    }

    def "test buildPageType not null"() {
        expect:
        mapper.buildPageType("1") == "1"
    }

    def "test buildPageType null"() {
        expect:
        mapper.buildPageType(null) == null
    }
}
