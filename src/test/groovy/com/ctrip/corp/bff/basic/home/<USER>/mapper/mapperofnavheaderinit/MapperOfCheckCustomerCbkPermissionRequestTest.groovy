package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit

import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/1/7 11:22
 * @Version 1.0
 */
class MapperOfCheckCustomerCbkPermissionRequestTest extends Specification {

    def mapper = new MapperOfCheckCustomerCbkPermissionRequest()

    def "Convert"() {
        when:
        def result = mapper.convert(Tuple1.of(new TemplateSoaRequestType(
                header: new TemplateHeader(
                        userId: "111",
                        corpId: "222"
                )
        )))
        then:
        result.getUid() == "111"
        result.getCorpId() == "222"
    }
}
