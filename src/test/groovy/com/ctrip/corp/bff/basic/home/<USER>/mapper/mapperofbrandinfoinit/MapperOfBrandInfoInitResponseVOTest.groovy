package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit

import com.ctrip.corp.bff.basic.home.contract.BrandInfo
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitResponseVO
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.*


class MapperOfBrandInfoInitResponseVOTest extends Specification {
    MapperOfBrandInfoInitResponseVO mapperOfBrandInfoInitResponseVO = new MapperOfBrandInfoInitResponseVO
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        BrandInfoInitResponseVO result = mapperOfBrandInfoInitResponseVO.convert(Tuple1.of(
                new BrandInfoInitResponseType(brandInfo: new BrandInfo())))

        then:
        result != null
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfBrandInfoInitResponseVO.check(null)

        then:
        result == null
    }
}