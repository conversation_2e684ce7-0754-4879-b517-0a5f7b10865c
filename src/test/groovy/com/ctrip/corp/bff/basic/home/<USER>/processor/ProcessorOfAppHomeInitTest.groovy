package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfCountInfoQueryRequestType
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfQueryApproveTaskRequestType
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitRequestVO
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient.HandlerOfQueryApproveTask
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfCountInfoQuery
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofapphomeinit.MapperOfAppHomeInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfGetCorpUserInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfProductInfoInitRequest
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType
import spock.lang.Specification

class ProcessorOfAppHomeInitTest extends Specification {

    def handlerOfProductInfoInit = Mock(HandlerOfProductInfoInit)
    def mapperOfProductInfoInitRequest = Mock(MapperOfProductInfoInitRequest)
    def handlerOfCustomGray = Mock(HandlerOfCustomGray)
    def mapperOfCustomGrayRequest = Mock(MapperOfCustomGrayRequest)
    def handlerOfGetCorpUserInfo = Mock(HandlerOfGetCorpUserInfo)
    def mapperOfGetCorpUserInfoRequest = Mock(MapperOfGetCorpUserInfoRequest)
    def handlerOfBrandInfoInit = Mock(HandlerOfBrandInfoInit)
    def mapperOfBrandInfoInitRequest = Mock(MapperOfBrandInfoInitRequest)
    def handlerOfQueryApproveTask = Mock(HandlerOfQueryApproveTask)
    def mapperOfQueryApproveTaskRequestType = Mock(MapperOfQueryApproveTaskRequestType)
    def handlerOfCountInfoQuery = Mock(HandlerOfCountInfoQuery)
    def mapperOfCountInfoQueryRequestType = Mock(MapperOfCountInfoQueryRequestType)
    def mapperOfAppHomeInitResponseVO = Mock(MapperOfAppHomeInitResponseVO)
    def processor = Spy(new ProcessorOfAppHomeInit(
            handlerOfProductInfoInit: handlerOfProductInfoInit,
            mapperOfProductInfoInitRequest: mapperOfProductInfoInitRequest,
            handlerOfCustomGray: handlerOfCustomGray,
            mapperOfCustomGrayRequest: mapperOfCustomGrayRequest,
            handlerOfGetCorpUserInfo: handlerOfGetCorpUserInfo,
            mapperOfGetCorpUserInfoRequest: mapperOfGetCorpUserInfoRequest,
            handlerOfBrandInfoInit: handlerOfBrandInfoInit,
            mapperOfBrandInfoInitRequest: mapperOfBrandInfoInitRequest,
            handlerOfQueryApproveTask: handlerOfQueryApproveTask,
            mapperOfQueryApproveTaskRequestType: mapperOfQueryApproveTaskRequestType,
            handlerOfCountInfoQuery: handlerOfCountInfoQuery,
            mapperOfCountInfoQueryRequestType: mapperOfCountInfoQueryRequestType,
            mapperOfAppHomeInitResponseVO: mapperOfAppHomeInitResponseVO,
    ))

    def "testExecute"() {
        given:
        AppHomeInitRequestVO request = new AppHomeInitRequestVO()
        when:
        processor.execute(request)
        then:
        1 * mapperOfBrandInfoInitRequest.map(_) >> new BrandInfoInitRequestType()
        1 * handlerOfBrandInfoInit.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfProductInfoInitRequest.map(_) >> new ProductInfoInitRequestType()
        1 * handlerOfProductInfoInit.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfCustomGrayRequest.map(_) >> new CustomGrayRequestType()
        1 * handlerOfCustomGray.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfQueryApproveTaskRequestType.map(_) >> new QueryApproveTaskRequestType()
        1 * handlerOfQueryApproveTask.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfGetCorpUserInfoRequest.map(_) >> new GetCorpUserInfoRequestType()
        1 * handlerOfGetCorpUserInfo.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfCountInfoQueryRequestType.map(_) >> new CountInfoQueryRequestType()
        1 * handlerOfCountInfoQuery.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfAppHomeInitResponseVO.map(_) >> null
    }

    def "testTracking"() {
        given:
        AppHomeInitRequestVO request = new AppHomeInitRequestVO()
        AppHomeInitResponseVO response = new AppHomeInitResponseVO()
        when:
        Map<String, String> result = processor.tracking(request, response)
        then:
        result == null
    }

}
