package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfCountInfoQueryRequestType
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfQueryApproveTaskRequestType
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitRequestVO
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient.HandlerOfQueryApproveTask
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfCountInfoQuery
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappmenuinit.MapperOfAppMenuInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfProductInfoInitRequest
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType
import spock.lang.Specification

class ProcessorOfAppMenuInitTest extends Specification {

    def handlerOfBrandInfoInit = Mock(HandlerOfBrandInfoInit)
    def mapperOfBrandInfoInitRequest = Mock(MapperOfBrandInfoInitRequest)
    def handlerOfProductInfoInit = Mock(HandlerOfProductInfoInit)
    def mapperOfProductInfoInitRequest = Mock(MapperOfProductInfoInitRequest)
    def handlerOfCustomGray = Mock(HandlerOfCustomGray)
    def mapperOfCustomGrayRequest = Mock(MapperOfCustomGrayRequest)
    def handlerOfGeneralSearchAccountInfo = Mock(HandlerOfGeneralSearchAccountInfo)
    def mapperofSoaGeneralSearchAccountInfoRequest = Mock(MapperofSoaGeneralSearchAccountInfoRequest)
    def handlerOfQueryApproveTask = Mock(HandlerOfQueryApproveTask)
    def mapperOfQueryApproveTaskRequestType = Mock(MapperOfQueryApproveTaskRequestType)
    def handlerOfCountInfoQuery = Mock(HandlerOfCountInfoQuery)
    def mapperOfCountInfoQueryRequestType = Mock(MapperOfCountInfoQueryRequestType)
    def mapperOfAppMenuInitResponseVO = Mock(MapperOfAppMenuInitResponseVO)
    def processor = Spy(new ProcessorOfAppMenuInit(
            handlerOfBrandInfoInit: handlerOfBrandInfoInit,
            mapperOfBrandInfoInitRequest: mapperOfBrandInfoInitRequest,
            handlerOfProductInfoInit: handlerOfProductInfoInit,
            mapperOfProductInfoInitRequest: mapperOfProductInfoInitRequest,
            handlerOfCustomGray: handlerOfCustomGray,
            mapperOfCustomGrayRequest: mapperOfCustomGrayRequest,
            handlerOfGeneralSearchAccountInfo: handlerOfGeneralSearchAccountInfo,
            mapperofSoaGeneralSearchAccountInfoRequest: mapperofSoaGeneralSearchAccountInfoRequest,
            handlerOfQueryApproveTask: handlerOfQueryApproveTask,
            mapperOfQueryApproveTaskRequestType: mapperOfQueryApproveTaskRequestType,
            handlerOfCountInfoQuery: handlerOfCountInfoQuery,
            mapperOfCountInfoQueryRequestType: mapperOfCountInfoQueryRequestType,
            mapperOfAppMenuInitResponseVO: mapperOfAppMenuInitResponseVO
    ))

    def "testExecute"() {
        given:
        AppMenuInitRequestVO request = new AppMenuInitRequestVO()
        TemplateSoaRequestType templateSoaRequestType = new TemplateSoaRequestType()
        request.setRequestHeader(templateSoaRequestType)
        TemplateHeader templateHeader = new TemplateHeader()
        templateSoaRequestType.setHeader(templateHeader)
        when:
        processor.execute(request)
        then:
        1 * mapperOfBrandInfoInitRequest.map(_) >> new BrandInfoInitRequestType()
        1 * handlerOfBrandInfoInit.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfProductInfoInitRequest.map(_) >> new ProductInfoInitRequestType()
        1 * handlerOfProductInfoInit.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfCustomGrayRequest.map(_) >> new CustomGrayRequestType()
        1 * handlerOfCustomGray.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperofSoaGeneralSearchAccountInfoRequest.map(_) >> new GeneralSearchAccountInfoRequestType()
        1 * handlerOfGeneralSearchAccountInfo.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfQueryApproveTaskRequestType.map(_) >> new QueryApproveTaskRequestType()
        1 * handlerOfQueryApproveTask.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfCountInfoQueryRequestType.map(_) >> new CountInfoQueryRequestType()
        1 * handlerOfCountInfoQuery.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfAppMenuInitResponseVO.map(_) >> null
    }

    def "testTracking"() {
        given:
        AppMenuInitRequestVO request = new AppMenuInitRequestVO()
        AppMenuInitResponseVO response = new AppMenuInitResponseVO()
        when:
        Map<String, String> result = processor.tracking(request, response)
        then:
        result == null
    }

}
