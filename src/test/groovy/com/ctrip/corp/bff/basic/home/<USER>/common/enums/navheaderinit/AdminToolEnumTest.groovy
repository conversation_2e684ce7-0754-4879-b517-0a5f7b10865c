package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit

import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/1/14 14:59
 * @Version 1.0
 */
class AdminToolEnumTest extends Specification {
    void setup() {
        ThreadLocalProvider.getOrCreate().setLanguage("en-US")
    }

    void cleanup() {
        ThreadLocalProvider.remove()
    }

    def "GetUrl"() {
        when:
        def result = AdminToolEnum.SETTLEMENT_MANAGEMENT.getUrl(["settlementManagementUrl": "/cbookingnew/auto/newBackLogin?source={0}&locale={1}"])
        then:
        result == "/cbookingnew/auto/newBackLogin?source=BLUE_SPACE&locale=en-US"
    }
}
