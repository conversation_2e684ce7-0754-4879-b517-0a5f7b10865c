package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import spock.lang.Specification

class MapperOfGetCityBaseInfoRequestTest extends Specification {

    def mapper = new MapperOfGetCityBaseInfoRequest()

    def "Convert"() {
        def param = Tuple2.of(Arrays.asList("abc"), new TemplateSoaRequestType())
        expect:
        null != mapper.convert(param)

    }
}
