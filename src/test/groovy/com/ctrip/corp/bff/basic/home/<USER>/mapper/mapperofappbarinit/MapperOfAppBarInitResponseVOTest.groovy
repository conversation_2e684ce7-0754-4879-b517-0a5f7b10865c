package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappbarinit

import com.ctrip.corp.bff.basic.contract.TabBarVO
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant
import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.AppBarConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.AppBarInfo
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.HostSiteConfig
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType
import com.google.common.collect.Maps
import spock.lang.Specification

class MapperOfAppBarInitResponseVOTest extends Specification {

    def "testConvert1"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        TemplateSoaRequestType request = new TemplateSoaRequestType()
        CustomGrayResponseType response = new CustomGrayResponseType()
        AppBarConfig appBarConfig = new AppBarConfig()
        HostSiteConfig hostSiteConfig = new HostSiteConfig()
        when:
        AppBarInitResponseVO result = mapper.convert(Tuple4.of(request, response, appBarConfig, hostSiteConfig))
        appBarConfig.setOldVersionAppBar(null)
        appBarConfig.setNewVersionAppBar(null)
        hostSiteConfig.setHostSite(null)
        then:
        true
    }

    def "testConvert2"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        TemplateSoaRequestType request = new TemplateSoaRequestType()
        CustomGrayResponseType response = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.APP_NEW_VERSION, "T")
        response.setResults(results)
        AppBarConfig appBarConfig = new AppBarConfig()
        HostSiteConfig hostSiteConfig = new HostSiteConfig()
        when:
        AppBarInitResponseVO result = mapper.convert(Tuple4.of(request, response, appBarConfig, hostSiteConfig))
        then:
        true
    }

    def "testIsNewVersion"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        when:
        boolean result = mapper.isNewVersion(null)
        then:
        !result
    }

    def "testBuildTabBarList"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        Map<String, List<AppBarInfo>> appBarMenu = Maps.newHashMap()
        TemplateSoaRequestType request = new TemplateSoaRequestType()
        HostSiteConfig hostSiteConfig = new HostSiteConfig()
        AppBarInfo appBarInfo = new AppBarInfo()
        when:
        List<TabBarVO> result = mapper.buildTabBarList(appBarMenu, request, hostSiteConfig)
        appBarInfo.setCode("1")
        appBarInfo.setName("1")
        appBarInfo.setNameColor("1")
        appBarInfo.setNameColor("1")
        appBarInfo.setSelectedNameColor("1")
        appBarInfo.setIconUrl("1")
        appBarInfo.setSelectedIconUrl("1")
        appBarInfo.setRedirectUrl("1")
        appBarInfo.setSharkKey("1")
        then:
        result.size() == 0
    }

    def "testBuildTabBarVO"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        AppBarInfo appBarInfo = new AppBarInfo()
        when:
        TabBarVO tabBar = mapper.buildTabBarVO(appBarInfo)
        then:
        true
    }

    def "testCheck"() {
        given:
        MapperOfAppBarInitResponseVO mapper = new MapperOfAppBarInitResponseVO()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

}
