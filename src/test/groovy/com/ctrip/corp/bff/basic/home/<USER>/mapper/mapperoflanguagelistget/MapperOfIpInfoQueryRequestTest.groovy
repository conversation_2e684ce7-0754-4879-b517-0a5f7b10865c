package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.tools.contract.IpInfoQueryRequestType
import spock.lang.*


class MapperOfIpInfoQueryRequestTest extends Specification {
    MapperOfIpInfoQueryRequest mapperOfIpInfoQueryRequest = new MapperOfIpInfoQueryRequest
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        IpInfoQueryRequestType result = mapperOfIpInfoQueryRequest.convert(Tuple1.of(new TemplateSoaRequestType()))

        then:
        result != null
    }

}