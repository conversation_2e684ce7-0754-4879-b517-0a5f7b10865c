package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitRequestVO
import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappbarinit.MapperOfAppBarInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType
import spock.lang.Specification

class ProcessorOfAppBarInitTest extends Specification {

    def handlerOfCustomGray = Mock(HandlerOfCustomGray)
    def mapperOfCustomGrayRequest = Mock(MapperOfCustomGrayRequest)
    def mapperOfAppBarInitResponseVO = Mock(MapperOfAppBarInitResponseVO)
    def processor = Spy(new ProcessorOfAppBarInit(
            handlerOfCustomGray: handlerOfCustomGray,
            mapperOfCustomGrayRequest: mapperOfCustomGrayRequest,
            mapperOfAppBarInitResponseVO: mapperOfAppBarInitResponseVO
    ))

    def "testExecute"() {
        given:
        AppBarInitRequestVO request = new AppBarInitRequestVO()
        when:
        processor.execute(request)
        then:
        1 * mapperOfCustomGrayRequest.map(_) >> new CustomGrayRequestType()
        1 * handlerOfCustomGray.handleAsync(_) >> new WaitFuture<>(null, null, null, null, null, null, null)
        1 * mapperOfAppBarInitResponseVO.map(_) >> null
    }

    def "testTracking"() {
        given:
        AppBarInitRequestVO request = new AppBarInitRequestVO()
        AppBarInitResponseVO response = new AppBarInitResponseVO()
        when:
        Map<String, String> result = processor.tracking(request, response)
        then:
        result == null
    }

}
