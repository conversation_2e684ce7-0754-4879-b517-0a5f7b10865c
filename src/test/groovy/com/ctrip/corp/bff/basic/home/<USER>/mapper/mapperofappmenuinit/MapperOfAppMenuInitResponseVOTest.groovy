package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappmenuinit

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType
import com.ctrip.corp.bff.basic.contract.GroupInfoVO
import com.ctrip.corp.bff.basic.contract.MenuUnitInfoVO
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType
import com.ctrip.corp.bff.basic.home.contract.ProductInfo
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant
import com.ctrip.corp.bff.basic.home.trip.common.constant.GeneralSearchAccountInfoField
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitResponseVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuGroupInfo
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuInfo
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7
import com.ctrip.corp.bff.im.contract.CountInfo
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import spock.lang.Specification

class MapperOfAppMenuInitResponseVOTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        BrandInfoInitResponseType brandInfoInitResponseType = new BrandInfoInitResponseType()
        ProductInfoInitResponseType productInfoInitResponseType = new ProductInfoInitResponseType()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        CountInfoQueryResponseType countInfoQueryResponseType = new CountInfoQueryResponseType()
        AppMenuConfig appMenuConfig = new AppMenuConfig()
        when:
        AppMenuInitResponseVO result = mapper.convert(Tuple7.of(brandInfoInitResponseType, productInfoInitResponseType,
                customGrayResponseType, generalSearchAccountInfoResponseType, queryApproveTaskResponseType,
                countInfoQueryResponseType, appMenuConfig))
        appMenuConfig.setGroupList(null)
        appMenuConfig.setMenuList(null)
        then:
        true
    }

    def "testCheck"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

    def "testBuildGroupInfoList"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        AppMenuGroupInfo appMenuGroupInfo = new AppMenuGroupInfo()
        appMenuGroupInfo.setCode("1")
        List<AppMenuGroupInfo> appMenuGroupInfoList = Lists.newArrayList(appMenuGroupInfo)
        Map<String, GroupInfoVO> map = Maps.newHashMap()
        map.put("1", new GroupInfoVO())
        when:
        List<GroupInfoVO> result = mapper.buildGroupInfoList(appMenuGroupInfoList, map)
        appMenuGroupInfo.getIcon()
        appMenuGroupInfo.setIcon("1")
        appMenuGroupInfo.setUrl("1")
        appMenuGroupInfo.getSort()
        appMenuGroupInfo.setSort(1)
        then:
        true
    }

    def "testBuildTravelRequestGroup1"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(null, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildTravelRequestGroup2"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildTravelRequestGroup3"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, "F")
        customGrayResponseType.setResults(results)
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildTravelRequestGroup4"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, "T")
        customGrayResponseType.setResults(results)
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, null)
        then:
        true
    }

    def "testBuildTravelRequestGroup5"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, "T")
        customGrayResponseType.setResults(results)
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildTravelRequestGroup6"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, "T")
        customGrayResponseType.setResults(results)
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        generalSearchAccountInfoResponseType.setResults(results)
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildTravelRequestGroup7"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, "T")
        results.put(CommonConstant.BILL_CONTROL_MODE, CommonConstant.BILL_CONTROL_MODE_TRAVEL_REQUEST)
        customGrayResponseType.setResults(results)
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        generalSearchAccountInfoResponseType.setResults(results)
        when:
        GroupInfoVO result = mapper.buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType)
        then:
        true
    }

    def "testBuildBookTripGroupMenuList"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        ProductInfoInitResponseType productInfoInitResponseType = new ProductInfoInitResponseType()
        ProductInfo productInfo = new ProductInfo()
        productInfo.setShow("T")
        productInfoInitResponseType.setProductInfos(Lists.newArrayList(productInfo, null))
        List<AppMenuInfo> appMenuInfoList = Lists.newArrayList()
        when:
        List<MenuUnitInfoVO> result = mapper.buildBookTripGroupMenuList(productInfoInitResponseType, appMenuInfoList)
        then:
        true
    }

    def "testBuildMenuInfoList"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        AppMenuInfo appMenuInfo = new AppMenuInfo()
        appMenuInfo.setCode("1")
        List<AppMenuInfo> appMenuInfoList = Lists.newArrayList(appMenuInfo)
        Map<String, MenuUnitInfoVO> map = Maps.newHashMap()
        map.put("1", new MenuUnitInfoVO())
        when:
        List<MenuUnitInfoVO> result = mapper.buildMenuInfoList(appMenuInfoList, map)
        appMenuInfo.setIcon("1")
        appMenuInfo.setUrl("1")
        appMenuInfo.setGroupCode("1")
        appMenuInfo.getSort()
        appMenuInfo.setSort(1)
        then:
        true
    }

    def "testBuildCountInfoMap"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        CountInfo countInfo1 = new CountInfo()
        CountInfo countInfo2 = new CountInfo()
        countInfo2.setKey("1")
        CountInfo countInfo3 = new CountInfo()
        countInfo3.setKey("1")
        countInfo2.setCount(2)
        CountInfoQueryResponseType countInfoQueryResponseType = new CountInfoQueryResponseType()
        countInfoQueryResponseType.setCountInfos(Lists.newArrayList(countInfo1, countInfo2, countInfo3, null))
        when:
        Map<String, Integer> result = mapper.buildCountInfoMap(countInfoQueryResponseType)
        then:
        true
    }

    def "buildManageTripGroupMenuList: itinerary"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        AppMenuInfo appMenuInfoJourney = new AppMenuInfo()
        appMenuInfoJourney.setCode("journey")
        AppMenuInfo itineraryJourney = new AppMenuInfo()
        itineraryJourney.setCode("itinerary")
        List<AppMenuInfo> appMenuInfoList = Arrays.asList(
                appMenuInfoJourney, itineraryJourney
        )

        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        Map<String, String> results = new HashMap<>()
        results.put(GeneralSearchAccountInfoField.BILL_TYPE, "E")
        results.put(GeneralSearchAccountInfoField.IS_SEN_ITINERARY, "F")
        generalSearchAccountInfoResponseType.setResults(results)
        when:
        def result = mapper.buildManageTripGroupMenuList(appMenuInfoList, generalSearchAccountInfoResponseType)
        then:
        result != null
        result.size() == 1
        result.get(0).getCode() == "itinerary"
    }

    def "buildManageTripGroupMenuList: journey"() {
        given:
        MapperOfAppMenuInitResponseVO mapper = new MapperOfAppMenuInitResponseVO()
        AppMenuInfo appMenuInfoJourney = new AppMenuInfo()
        appMenuInfoJourney.setCode("journey")
        AppMenuInfo itineraryJourney = new AppMenuInfo()
        itineraryJourney.setCode("itinerary")
        List<AppMenuInfo> appMenuInfoList = Arrays.asList(
                appMenuInfoJourney, itineraryJourney
        )

        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        Map<String, String> results = new HashMap<>()
        results.put(GeneralSearchAccountInfoField.BILL_TYPE, "E")
        results.put(GeneralSearchAccountInfoField.IS_SEN_ITINERARY, "T")
        generalSearchAccountInfoResponseType.setResults(results)
        when:
        def result = mapper.buildManageTripGroupMenuList(appMenuInfoList, generalSearchAccountInfoResponseType)
        then:
        result != null
        result.size() == 1
        result.get(0).getCode() == "journey"
    }

}
