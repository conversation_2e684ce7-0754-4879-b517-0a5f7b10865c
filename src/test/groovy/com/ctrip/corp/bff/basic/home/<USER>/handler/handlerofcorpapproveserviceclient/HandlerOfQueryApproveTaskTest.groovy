package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient

import spock.lang.Specification

class HandlerOfQueryApproveTaskTest extends Specification {

    def "testGetMethodName"() {
        given:
        HandlerOfQueryApproveTask handler = new HandlerOfQueryApproveTask()
        when:
        String result = handler.getMethodName()
        then:
        result == "queryApproveTask"
    }

}
