package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient

import spock.lang.Specification

class HandlerOfCountInfoQueryTest extends Specification {

    def "testGetMethodName"() {
        given:
        HandlerOfCountInfoQuery handler = new HandlerOfCountInfoQuery()
        when:
        String result = handler.getMethodName()
        then:
        result == "countInfoQuery"
    }

}
