package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery

import com.ctrip.corp.bff.basic.home.trip.common.enums.DocumentProductEnum
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflegaldocumentquery.MapperOfLegalDocumentResponse
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.ContactUsInfoByCountryCodeConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigItem
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum
import com.google.common.collect.Lists
import corp.user.service.corp4jservice.GetCorpInfoResponseType
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfLegalDocumentResponseTest extends Specification {
    def mapperOfLegalDocumentResponse = new MapperOfLegalDocumentResponse()
    def mapperOfLanguageList = Mock(MapperOfLanguageList.class)

    void setup() {
        mapperOfLegalDocumentResponse.mapperOfLanguageList = mapperOfLanguageList
    }

    @Unroll
    def legalDocumentQueryTest() {
        given:
        when:
        LegalDocumentConfigItem legalDocumentConfigItem = new LegalDocumentConfigItem()
        legalDocumentConfigItem.setPos(PosEnum.XX.getSiteCode())
        legalDocumentConfigItem.setSiteTypeList(Lists.newArrayList("hotel"))
        legalDocumentConfigItem.setPageList(Lists.newArrayList("booking"))
        legalDocumentConfigItem.setProduct(DocumentProductEnum.PRIVACY_STATEMENT.getCode())
        legalDocumentConfigItem.setLanguageList(Lists.newArrayList(LanguageLocaleEnum.EN_US.getLanguageLocaleString()))
        legalDocumentConfigItem.setPageUrl("test001")
        legalDocumentConfigItem.setPdfUrl("test002")
        LegalDocumentConfigItem legalDocumentConfigItem2 = new LegalDocumentConfigItem()
        legalDocumentConfigItem2.setPos(PosEnum.XX.getSiteCode())
        legalDocumentConfigItem2.setSiteTypeList(Lists.newArrayList("hotel"))
        legalDocumentConfigItem2.setPageList(Lists.newArrayList("booking"))
        legalDocumentConfigItem2.setProduct(DocumentProductEnum.TERMS_AND_CONDITIONS.getCode())
        legalDocumentConfigItem2.setLanguageList(Lists.newArrayList(LanguageLocaleEnum.EN_US.getLanguageLocaleString()))
        legalDocumentConfigItem2.setPageUrl("test003")
        legalDocumentConfigItem2.setPdfUrl("test004")
        List<LegalDocumentConfigItem> lists = new ArrayList<>()
        lists.add(legalDocumentConfigItem)
        lists.add(legalDocumentConfigItem2)

        def result = mapperOfLegalDocumentResponse.convert(Tuple4.of(new LegalDocumentQueryRequestVO(), lists, new GetCorpInfoResponseType(), "1"))

        then:
        "" == result.getPrivacyStatementPdfUrl()
        "" == result.getTermsAndConditionsPdfUrl()

    }

    def "getContactUsEmail"() {
        given:
        mapperOfLegalDocumentResponse.list = [new ContactUsInfoByCountryCodeConfig(countryCode: "CN", email: "test001")]
        when:
        String result = mapperOfLegalDocumentResponse.getContactUsEmail("CN")
        then:
        result != null


    }

}
