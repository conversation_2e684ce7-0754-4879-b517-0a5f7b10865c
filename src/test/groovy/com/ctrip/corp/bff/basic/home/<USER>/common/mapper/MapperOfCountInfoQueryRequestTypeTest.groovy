package com.ctrip.corp.bff.basic.home.trip.common.mapper

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType
import com.google.common.collect.Lists
import spock.lang.Specification

class MapperOfCountInfoQueryRequestTypeTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfCountInfoQueryRequestType mapper = new MapperOfCountInfoQueryRequestType()
        TemplateSoaRequestType templateSoaRequestType = new TemplateSoaRequestType()
        List<String> list = Lists.newArrayList("1")
        when:
        CountInfoQueryRequestType result = mapper.convert(Tuple2.of(templateSoaRequestType, list))
        then:
        result.getQueryKeys().size() == 1
    }

    def "testCheck"() {
        given:
        MapperOfCountInfoQueryRequestType mapper = new MapperOfCountInfoQueryRequestType()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

}
