package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit

import com.ctrip.corp.bff.basic.home.contract.SideBarInitRequestType
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitRequestVO
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

class MapperOfSideBarInitRequestTypeTest extends Specification {

    def "testConvert"() {
        given:
        MapperOfSideBarInitRequestType mapper = new MapperOfSideBarInitRequestType()
        SideBarInitRequestVO requestVO = new SideBarInitRequestVO()
        when:
        SideBarInitRequestType result = mapper.convert(Tuple1.of(requestVO))
        then:
        true
    }

    def "testCheck"() {
        given:
        MapperOfSideBarInitRequestType mapper = new MapperOfSideBarInitRequestType()
        when:
        ParamCheckResult result = mapper.check(null)
        then:
        result == null
    }

}
