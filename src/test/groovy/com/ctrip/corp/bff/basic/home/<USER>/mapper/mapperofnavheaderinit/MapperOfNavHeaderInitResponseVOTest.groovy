package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType
import com.ctrip.corp.bff.basic.home.contract.ProductInfo
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType
import com.ctrip.corp.bff.basic.home.trip.builder.NavHeaderInitResponse
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant
import com.ctrip.corp.bff.basic.home.trip.common.constant.GeneralSearchAccountInfoField
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.ManageTripEnum
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList
import com.ctrip.corp.bff.basic.home.trip.qconfig.payment.CardInfo
import com.ctrip.corp.bff.basic.home.trip.qconfig.payment.PayCardType
import com.ctrip.corp.bff.basic.home.trip.qconfig.payment.PaymentAgreementSignCompanyInfoConfig
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType
import com.ctrip.corp.bff.mcinfo.contract.MenuInfoType
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitResponseType
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.authorityManage.*
import corp.user.service.corp4jservice.GetCorpInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification
import spock.lang.Unroll
/**
 * @Author: z.c. wang
 * @Date: 2024/12/10 10:05
 * @Version 1.0
 */
class MapperOfNavHeaderInitResponseVOTest extends Specification {

    def mapper = new MapperOfNavHeaderInitResponseVO(
            navHeaderUrlConfig: new HashMap<String, String>(),
            mapperOfLanguageList: new MapperOfLanguageList()
    )

    @Unroll
    def "Convert"() {
        given:
        def result
        when: "admin"
        def getCorpUserInfoResponseType = new GetCorpUserInfoResponseType(
                preferredFirstName: "preferredFirstName",
                preferredLastName: "preferredLastName",
                corpAdminPermission: "T"
        )
        def getCorpInfoResponseType = new GetCorpInfoResponseType(
                accountType: 8
        )
        def generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType(
                results: [
                        BillControlMode: "A"
                ]
        )
        def customGrayResponseType = new CustomGrayResponseType(
                results: [biz_approval_endorsement_switch: "T"]
        )
        def product = new ProductInfoInitResponseType(
                productInfos: [
                        new ProductInfo(
                                productLine: "FLIGHT",
                                show: "T"
                        ),
                        new ProductInfo(
                                productLine: "HOTEL",
                                show: "T"
                        ),
                        new ProductInfo(
                                productLine: "TRAIN",
                                show: "F"
                        ),
                        new ProductInfo(
                                productLine: "INTL_CAR_CH",
                                show: "F"
                        )
                ]
        )
        def checkAuthResponseType = new CheckAuthResponseType(
                centerAuthData: new CheckCenterAuthDataType(
                        functionUnlimited: true
                )
        )
        def checkCustomerCbkPermissionResponseType = new CheckCustomerCbkPermissionResponseType(
                hasPermission: true
        )
        def myProfileMenuInitResponseType = new MyProfileMenuInitResponseType(
                menus: [
                        new MenuInfoType(
                                code: "ENDORSEMENT"
                        ),
                        new MenuInfoType(
                                code: "PERSONAL_INFO"
                        ),
                        new MenuInfoType(
                                code: "TRAVEL_DOCUMENT"
                        ),
                        new MenuInfoType(
                                code: "GUEST_TRAVELER_INFO"
                        ),
                        new MenuInfoType(
                                code: "PAYMENT_CARD"
                        ),
                        new MenuInfoType(
                                code: "LOYALTY_MEMBERSHIP"
                        ),
                        new MenuInfoType(
                                code: "DELEGATE_APPROVAL"
                        ),
                        new MenuInfoType(
                                code: "INBOX"
                        ),
                        new MenuInfoType(
                                code: "WALLET"
                        ),
                        new MenuInfoType(
                                code: "LOGOUT"
                        )
                ]
        )
        def imTripInitResponse = new ImTripInitResponseType(

        )
        def requestVO = new NavHeaderInitRequestVO()
        NavHeaderInitResponse navHeaderInitResponse = NavHeaderInitResponse.builder()
                .setGetCorpInfoResponseType(getCorpInfoResponseType)
                .setGetCorpUserInfoResponseType(getCorpUserInfoResponseType)
                .setQueryAuthResponseType(null)
                .setCheckAuthResponseType(checkAuthResponseType)
                .setGeneralSearchAccountInfoResponseType(generalSearchAccountInfoResponseType)
                .setCustomGrayResponseType(customGrayResponseType)
                .setProductInfoInitResponseType(product)
                .setCheckCustomerCbkPermissionResponseType(checkCustomerCbkPermissionResponseType)
                .setBrandInfoInitResponseType(null)
                .setMyProfileMenuInitResponseType(myProfileMenuInitResponseType)
                .setImTripInitResponse(imTripInitResponse)
                .setCheckRequest(true)
                .setNavHeaderInitRequestVO(requestVO)
                .build();
        result = mapper.map(Tuple1.of(navHeaderInitResponse))
        then:
        result.getUserInfo().getPreferFirstName() == "preferredFirstName"
        result.getUserInfo().getPreferLastName() == "preferredLastName"
        result.getPlanTripMenuInfo().getMenus().size() == 2
        result.getAdminToolMenuInfo().getMenus().size() == 5
        result.getManageTripMenuInfo().getMenus().size() == 5
        when: "reporting"
        getCorpInfoResponseType = new GetCorpInfoResponseType(
                accountType: 1
        )
        checkAuthResponseType = new CheckAuthResponseType(
                centerAuthData: new CheckCenterAuthDataType(
                        functionUnlimited: false,
                        functions: [new CenterAuthFunctionType(
                                        code: "userTotal"
                        )]
                )
        )
        def queryAuthResponseType = new QueryAuthResponseType(
                centerAuthData: new QueryCenterAuthDataType(
                        centerAuthInfos: [new CenterAuthInfoType(
                                functionContents: [new FunctionContentType(
                                        code: "userTotal"
                                )]
                        )]
                )
        )
        navHeaderInitResponse = NavHeaderInitResponse.builder()
                .setGetCorpInfoResponseType(getCorpInfoResponseType)
                .setGetCorpUserInfoResponseType(getCorpUserInfoResponseType)
                .setQueryAuthResponseType(null)
                .setCheckAuthResponseType(checkAuthResponseType)
                .setGeneralSearchAccountInfoResponseType(generalSearchAccountInfoResponseType)
                .setCustomGrayResponseType(customGrayResponseType)
                .setProductInfoInitResponseType(product)
                .setCheckCustomerCbkPermissionResponseType(checkCustomerCbkPermissionResponseType)
                .setBrandInfoInitResponseType(null)
                .setMyProfileMenuInitResponseType(null)
                .setImTripInitResponse(null)
                .setCheckRequest(true)
                .setNavHeaderInitRequestVO(requestVO)
                .build();
        result = mapper.map(Tuple1.of(navHeaderInitResponse))
        then:
        result.getUserInfo().getPreferFirstName() == "preferredFirstName"
        result.getUserInfo().getPreferLastName() == "preferredLastName"
        result.getPlanTripMenuInfo().getMenus().size() == 2
        result.getManageTripMenuInfo().getMenus().size() == 5
    }

    def "testCheckPayment1"() {
        given:
        String agreementSignCompany = "a"
        PaymentAgreementSignCompanyInfoConfig paymentAgreementSignCompanyInfoConfig = new PaymentAgreementSignCompanyInfoConfig()
        mapper.paymentAgreementSignCompanyInfoConfig = paymentAgreementSignCompanyInfoConfig
        when:
        boolean result = mapper.checkPayment(agreementSignCompany)
        then:
        !result
    }

    def "testCheckPayment2"() {
        given:
        String agreementSignCompany = "a"
        PaymentAgreementSignCompanyInfoConfig paymentAgreementSignCompanyInfoConfig = new PaymentAgreementSignCompanyInfoConfig()
        mapper.paymentAgreementSignCompanyInfoConfig = paymentAgreementSignCompanyInfoConfig
        Map<String, List<CardInfo>> map = Maps.newHashMap()
        paymentAgreementSignCompanyInfoConfig.setCompanyCardMap(map)
        when:
        boolean result = mapper.checkPayment(agreementSignCompany)
        then:
        !result
    }

    def "testCheckPayment3"() {
        given:
        String agreementSignCompany = "a"
        PaymentAgreementSignCompanyInfoConfig paymentAgreementSignCompanyInfoConfig = new PaymentAgreementSignCompanyInfoConfig()
        mapper.paymentAgreementSignCompanyInfoConfig = paymentAgreementSignCompanyInfoConfig
        Map<String, List<CardInfo>> map = Maps.newHashMap()
        paymentAgreementSignCompanyInfoConfig.setCompanyCardMap(map)
        map.put("b", Lists.newArrayList())
        CardInfo cardInfo = new CardInfo()
        cardInfo.setPayCardTypeList(cardInfo.getPayCardTypeList())
        cardInfo.setCardGroup(cardInfo.getCardGroup())
        PayCardType payCardType = new PayCardType()
        payCardType.setCardType(payCardType.getCardType())
        when:
        boolean result = mapper.checkPayment(agreementSignCompany)
        then:
        !result
    }

    def "testCheckPayment4"() {
        given:
        String agreementSignCompany = "a"
        PaymentAgreementSignCompanyInfoConfig paymentAgreementSignCompanyInfoConfig = new PaymentAgreementSignCompanyInfoConfig()
        mapper.paymentAgreementSignCompanyInfoConfig = paymentAgreementSignCompanyInfoConfig
        Map<String, List<CardInfo>> map = Maps.newHashMap()
        paymentAgreementSignCompanyInfoConfig.setCompanyCardMap(map)
        map.put("a", Lists.newArrayList())
        CardInfo cardInfo = new CardInfo()
        cardInfo.setPayCardTypeList(cardInfo.getPayCardTypeList())
        cardInfo.setCardGroup(cardInfo.getCardGroup())
        PayCardType payCardType = new PayCardType()
        payCardType.setCardType(payCardType.getCardType())
        when:
        boolean result = mapper.checkPayment(agreementSignCompany)
        then:
        !result
    }

    def "testSupportApproval1"() {
        given:
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        when:
        boolean result = mapper.supportApproval(null, queryApproveTaskResponseType)
        then:
        result
    }

    def "testSupportApproval2"() {
        given:
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        when:
        boolean result = mapper.supportApproval(customGrayResponseType, queryApproveTaskResponseType)
        then:
        result
    }

    def "testSupportApproval3"() {
        given:
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.APPROVAL_PERMISSION_NEW, "T")
        customGrayResponseType.setResults(results)
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        when:
        boolean result = mapper.supportApproval(customGrayResponseType, null)
        then:
        !result
    }

    def "testSupportApproval4"() {
        given:
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.APPROVAL_PERMISSION_NEW, "T")
        customGrayResponseType.setResults(results)
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        when:
        boolean result = mapper.supportApproval(customGrayResponseType, queryApproveTaskResponseType)
        then:
        !result
    }

    def "testSupportApproval5"() {
        given:
        CustomGrayResponseType customGrayResponseType = new CustomGrayResponseType()
        Map<String, String> results = Maps.newHashMap()
        results.put(CommonConstant.APPROVAL_PERMISSION_NEW, "T")
        customGrayResponseType.setResults(results)
        QueryApproveTaskResponseType queryApproveTaskResponseType = new QueryApproveTaskResponseType()
        queryApproveTaskResponseType.setExistApproveTask(true)
        when:
        boolean result = mapper.supportApproval(customGrayResponseType, queryApproveTaskResponseType)
        then:
        result
    }

    def "buildManageTripMenuInfo:Journey"() {
        given:
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        Map<String, String> results = new HashMap<>()
        results.put(GeneralSearchAccountInfoField.BILL_TYPE, "E")
        results.put(GeneralSearchAccountInfoField.IS_SEN_ITINERARY, "T")
        generalSearchAccountInfoResponseType.setResults(results)

        when:
        def result = mapper.buildManageTripMenuInfo(generalSearchAccountInfoResponseType, null, null)
        then:
        result.contains(ManageTripEnum.JOURNEYS)
    }

    def "buildManageTripMenuInfo:Itinerary"() {
        given:
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = new GeneralSearchAccountInfoResponseType()
        Map<String, String> results = new HashMap<>()
        results.put(GeneralSearchAccountInfoField.BILL_TYPE, "E")
        results.put(GeneralSearchAccountInfoField.IS_SEN_ITINERARY, "F")
        generalSearchAccountInfoResponseType.setResults(results)

        when:
        def result = mapper.buildManageTripMenuInfo(generalSearchAccountInfoResponseType, null, null)
        then:
        result.contains(ManageTripEnum.ITINERARY)
    }

    def "testHasRequestTotalAuth1"() {
        given:
        QueryAuthResponseType queryAuthResponseType = new QueryAuthResponseType()
        QueryCenterAuthDataType queryCenterAuthDataType = new QueryCenterAuthDataType()
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        queryCenterAuthDataType.setCenterAuthInfos(Lists.newArrayList(centerAuthInfoType))
        queryAuthResponseType.setCenterAuthData(queryCenterAuthDataType)
        centerAuthInfoType.setType("ADMIN")
        centerAuthInfoType.setSystem("BLUESPACE_ADMIN")
        when:
        boolean result = mapper.hasRequestTotalAuth(queryAuthResponseType)
        then:
        result
    }

    def "testHasRequestTotalAuth2"() {
        given:
        QueryAuthResponseType queryAuthResponseType = new QueryAuthResponseType()
        QueryCenterAuthDataType queryCenterAuthDataType = new QueryCenterAuthDataType()
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        queryCenterAuthDataType.setCenterAuthInfos(Lists.newArrayList(centerAuthInfoType))
        queryAuthResponseType.setCenterAuthData(queryCenterAuthDataType)
        centerAuthInfoType.setType("CUSTOM")
        centerAuthInfoType.setSystem("BLUESPACE_ADMIN")
        FunctionContentType functionContentType = new FunctionContentType()
        functionContentType.setCode("requestTotal")
        centerAuthInfoType.setFunctionContents(Lists.newArrayList(functionContentType))
        when:
        boolean result = mapper.hasRequestTotalAuth(queryAuthResponseType)
        then:
        !result
    }

    def "testHasRequestTotalAuth3"() {
        given:
        QueryAuthResponseType queryAuthResponseType = new QueryAuthResponseType()
        QueryCenterAuthDataType queryCenterAuthDataType = new QueryCenterAuthDataType()
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        queryCenterAuthDataType.setCenterAuthInfos(Lists.newArrayList(centerAuthInfoType))
        queryAuthResponseType.setCenterAuthData(queryCenterAuthDataType)
        centerAuthInfoType.setType("CUSTOM")
        centerAuthInfoType.setSystem("BLUESPACE_ADMIN")
        FunctionContentType functionContentType = new FunctionContentType()
        functionContentType.setCode("1")
        centerAuthInfoType.setFunctionContents(Lists.newArrayList(functionContentType))
        when:
        boolean result = mapper.hasRequestTotalAuth(queryAuthResponseType)
        then:
        !result
    }

    def "testIsRequestTotalValid"() {
        given:
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        Calendar beginCalendar = Calendar.getInstance()
        beginCalendar.add(Calendar.DAY_OF_MONTH, -1)
        centerAuthInfoType.setBeginTime(DateUtil.formatDate(beginCalendar.getTime(), DateUtil.YYYY_MM_DD))
        Calendar endCalendar = Calendar.getInstance()
        endCalendar.add(Calendar.DAY_OF_MONTH, 1)
        centerAuthInfoType.setExpireTime(DateUtil.formatDate(endCalendar.getTime(), DateUtil.YYYY_MM_DD))
        centerAuthInfoType.setType("CUSTOM")
        centerAuthInfoType.setSystem("BLUESPACE_ADMIN")
        centerAuthInfoType.setDataType("CORP")
        FunctionContentType functionContentType = new FunctionContentType()
        functionContentType.setCode("requestTotal")
        centerAuthInfoType.setFunctionContents(Lists.newArrayList(functionContentType))
        when:
        boolean result = mapper.isRequestTotalValid(centerAuthInfoType)
        then:
        result
    }

    def "testIsValidDate1"() {
        given:
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        Calendar beginCalendar = Calendar.getInstance()
        beginCalendar.add(Calendar.DAY_OF_MONTH, 1)
        centerAuthInfoType.setBeginTime(DateUtil.formatDate(beginCalendar.getTime(), DateUtil.YYYY_MM_DD))
        Calendar endCalendar = Calendar.getInstance()
        endCalendar.add(Calendar.DAY_OF_MONTH, 1)
        centerAuthInfoType.setExpireTime(DateUtil.formatDate(endCalendar.getTime(), DateUtil.YYYY_MM_DD))
        when:
        boolean result = mapper.isValidDate(centerAuthInfoType)
        then:
        !result
    }

    def "testIsValidDate2"() {
        given:
        CenterAuthInfoType centerAuthInfoType = new CenterAuthInfoType()
        Calendar beginCalendar = Calendar.getInstance()
        beginCalendar.add(Calendar.DAY_OF_MONTH, -1)
        centerAuthInfoType.setBeginTime(DateUtil.formatDate(beginCalendar.getTime(), DateUtil.YYYY_MM_DD))
        Calendar endCalendar = Calendar.getInstance()
        endCalendar.add(Calendar.DAY_OF_MONTH, -1)
        centerAuthInfoType.setExpireTime(DateUtil.formatDate(endCalendar.getTime(), DateUtil.YYYY_MM_DD))
        when:
        boolean result = mapper.isValidDate(centerAuthInfoType)
        then:
        !result
    }

}
