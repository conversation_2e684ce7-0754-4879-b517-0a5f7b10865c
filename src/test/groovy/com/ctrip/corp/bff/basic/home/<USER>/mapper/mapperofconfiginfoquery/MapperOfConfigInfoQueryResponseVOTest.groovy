package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofconfiginfoquery

import com.ctrip.corp.bff.basic.home.trip.common.enums.DocumentProductEnum
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryResponseVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentBO
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentConfigService
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.StatementBO
import com.ctrip.corp.bff.framework.template.common.language.LanguageUtil
import com.ctrip.corp.bff.framework.template.common.threadlocal.CorpMobileContext
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2025/7/24 16:04
 */
class MapperOfConfigInfoQueryResponseVOTest extends Specification {


    MapperOfConfigInfoQueryResponseVO mapper
    HostDocumentConfigService hostDocumentConfigService

    def setup() {
        hostDocumentConfigService = Mock(HostDocumentConfigService)
        mapper = new MapperOfConfigInfoQueryResponseVO()
        mapper.hostDocumentConfigService = hostDocumentConfigService

    }


    def "测试convert方法"() {
        given: "准备请求和模拟配置"
        def request = new ConfigInfoQueryRequestVO()

        def hostDocumentConfig = new HostDocumentConfig()
        def hostDocumentList = createTestHostDocumentList()
        hostDocumentConfig.setHostDocumentList(hostDocumentList)
        hostDocumentConfigService.getHostDocumentConfig() >> hostDocumentConfig

        // 模拟当前主机和语言
        CorpMobileContext context = ThreadLocalProvider.getOrCreate()
        context.setGatewayHost("www.test.com")
        context.setLanguage("en-US")

        when: "调用convert方法"
        def result = mapper.convert(Tuple1.of(request))

        then: "验证响应结果"
        result != null
        result instanceof ConfigInfoQueryResponseVO
        result.documentUrl != null
        result.documentUrl.privacyStatementUrl == "http://privacy-test.com"
        result.documentUrl.termsAndConditionUrl == "http://terms-test.com"
    }

    def "测试buildDocumentUrl方法 - 当前主机匹配"() {
        given: "配置服务和当前主机"
        def hostDocumentConfig = new HostDocumentConfig()
        def hostDocumentList = createTestHostDocumentList()
        hostDocumentConfig.setHostDocumentList(hostDocumentList)
        hostDocumentConfigService.getHostDocumentConfig() >> hostDocumentConfig


        // 模拟当前主机和语言
        CorpMobileContext context = ThreadLocalProvider.getOrCreate()
        context.setGatewayHost("www.test.com")
        context.setLanguage("en-US")


        when: "调用buildDocumentUrl方法"
        def result = mapper.buildDocumentUrl()

        then: "验证结果"
        result != null
        result.privacyStatementUrl == "http://privacy-test.com"
        result.termsAndConditionUrl == "http://terms-test.com"
    }

    def "测试buildDocumentUrl方法 - 使用默认主机"() {
        given: "配置服务和未知主机"
        def hostDocumentConfig = new HostDocumentConfig()
        def hostDocumentList = createTestHostDocumentList()
        hostDocumentConfig.setHostDocumentList(hostDocumentList)
        hostDocumentConfigService.getHostDocumentConfig() >> hostDocumentConfig
        // 模拟当前主机和语言
        CorpMobileContext context = ThreadLocalProvider.getOrCreate()
        context.setGatewayHost("www.unknown.com")
        context.setLanguage("en-US")

        when: "调用buildDocumentUrl方法"
        def result = mapper.buildDocumentUrl()

        then: "验证使用默认主机配置"
        result != null
        result.privacyStatementUrl == "http://privacy-default.com"
        result.termsAndConditionUrl == "http://terms-default.com"
    }

    def "测试filterDocument方法"() {
        given: "主机文档对象"
        def hostDocument = createTestHostDocumentBO("www.test.com")
        // 模拟当前主机和语言
        CorpMobileContext context = ThreadLocalProvider.getOrCreate()
        context.setLanguage("en-US")
        GroovyMock(LanguageUtil, global: true)


        when: "获取隐私声明文档"
        def privacyResult = mapper.filterDocument(hostDocument, DocumentProductEnum.PRIVACY_STATEMENT)

        and: "获取条款文档"
        def termsResult = mapper.filterDocument(hostDocument, DocumentProductEnum.TERMS_AND_CONDITIONS)

        then: "验证结果"
        privacyResult == "http://privacy-test.com"
        termsResult == "http://terms-test.com"
    }

    def "测试filterStatementBO方法"() {
        given: "声明列表"
        def statements = createTestStatementList()

        expect: "根据不同语言获取声明"
        mapper.filterStatementBO(statements, "en-US")?.documentUrl == "http://en-us.com"
        mapper.filterStatementBO(statements, "zh-CN")?.documentUrl == "http://zh-cn.com"
        mapper.filterStatementBO(statements, "fr-FR") == null
        mapper.filterStatementBO(null, "en-US") == null
    }

    def "测试filterHostDocumentBO方法"() {
        given: "主机文档列表"
        def hostDocuments = createTestHostDocumentList()

        expect: "根据不同主机名获取主机文档"
        mapper.filterHostDocumentBO(hostDocuments, "www.test.com") != null
        mapper.filterHostDocumentBO(hostDocuments, "mobile.test.com") != null // 正则匹配
        mapper.filterHostDocumentBO(hostDocuments, "www.other.com") == null
        mapper.filterHostDocumentBO(null, "www.test.com") == null
    }

    // 辅助方法：创建测试用的主机文档列表
    private List<HostDocumentBO> createTestHostDocumentList() {
        List<HostDocumentBO> hostDocuments = []

        // 普通主机文档
        hostDocuments.add(createTestHostDocumentBO("www.test.com"))

        // 正则表达式主机文档
        def regexHostDoc = new HostDocumentBO()
        regexHostDoc.setHostList([".*\\.test\\.com"])
        regexHostDoc.setPrivacyStatementList(createTestStatementList())
        regexHostDoc.setTermsAndConditionList(createTestStatementList())
        hostDocuments.add(regexHostDoc)

        // 默认主机文档
        def defaultHostDoc = new HostDocumentBO()
        defaultHostDoc.setHostList(["www.trip.biz"])

        def defaultPrivacyStatement = new StatementBO()
        defaultPrivacyStatement.setLanguageList(["en-US", "zh-CN"])
        defaultPrivacyStatement.setDocumentUrl("http://privacy-default.com")

        def defaultTermsStatement = new StatementBO()
        defaultTermsStatement.setLanguageList(["en-US", "zh-CN"])
        defaultTermsStatement.setDocumentUrl("http://terms-default.com")

        defaultHostDoc.setPrivacyStatementList([defaultPrivacyStatement])
        defaultHostDoc.setTermsAndConditionList([defaultTermsStatement])

        hostDocuments.add(defaultHostDoc)

        return hostDocuments
    }

    // 辅助方法：创建测试用的主机文档对象
    private HostDocumentBO createTestHostDocumentBO(String host) {
        HostDocumentBO hostDoc = new HostDocumentBO()
        hostDoc.setHostList([host])

        def privacyStatement = new StatementBO()
        privacyStatement.setLanguageList(["en-US", "zh-CN"])
        privacyStatement.setDocumentUrl("http://privacy-test.com")
        hostDoc.setPrivacyStatementList([privacyStatement])

        def termsStatement = new StatementBO()
        termsStatement.setLanguageList(["en-US", "zh-CN"])
        termsStatement.setDocumentUrl("http://terms-test.com")
        hostDoc.setTermsAndConditionList([termsStatement])

        return hostDoc
    }

    // 辅助方法：创建测试用的声明列表
    private List<StatementBO> createTestStatementList() {
        List<StatementBO> statements = []

        def enStatement = new StatementBO()
        enStatement.setLanguageList(["en-US"])
        enStatement.setDocumentUrl("http://en-us.com")
        statements.add(enStatement)

        def zhStatement = new StatementBO()
        zhStatement.setLanguageList(["zh-CN"])
        zhStatement.setDocumentUrl("http://zh-cn.com")
        statements.add(zhStatement)

        def defaultStatement = new StatementBO()
        defaultStatement.setLanguageList(["defaultLanguage"])
        defaultStatement.setDocumentUrl("http://default.com")
        statements.add(defaultStatement)

        return statements
    }
}
