package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery


import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.CommonBaseEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import spock.lang.Specification

class MapperOfGetCityBaseInfoResponseTest extends Specification {

    def mapper = new MapperOfGetCityBaseInfoResponse()

    def "Convert no country"() {
        def param = Tuple1.of(new GetCityBaseInfoResponseType())
        expect:
        null != mapper.convert(param)
    }

    def "Convert has country"() {
        def param = Tuple1.of(new GetCityBaseInfoResponseType(
                cityBaseInfo: Arrays.asList(new CityBaseInfoEntity(countryInfo: new CommonBaseEntity(id: 123)))))
        expect:
        null != mapper.convert(param)
    }
}
