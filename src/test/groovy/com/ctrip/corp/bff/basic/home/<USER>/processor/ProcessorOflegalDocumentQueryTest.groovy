package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfIpInfoQuery
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfIpInfoQueryRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflegaldocumentquery.MapperOfLegalDocumentResponse
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfig
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigService
import spock.lang.*


class ProcessorOflegalDocumentQueryTest extends Specification {
    LegalDocumentConfigService legalDocumentConfigService = Mock()
    MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest = Mock()
    HandlerOfGetCorpInfo handlerOfGetCorpInfo = Mock()
    MapperOfLegalDocumentResponse mapperOfLegalDocumentResponse = Mock()
    MapperOfIpInfoQueryRequest mapperOfGetIpInfoRequest = Mock()
    HandlerOfIpInfoQuery handlerOfIpInfoQuery = Mock()
    ProcessorOflegalDocumentQuery processorOflegalDocumentQuery = new ProcessorOflegalDocumentQuery
            (
                    legalDocumentConfigService: legalDocumentConfigService,
                    mapperOfSoaGetCorpInfoRequest: mapperOfSoaGetCorpInfoRequest,
                    handlerOfGetCorpInfo: handlerOfGetCorpInfo,
                    mapperOfLegalDocumentResponse: mapperOfLegalDocumentResponse,
                    mapperOfGetIpInfoRequest: mapperOfGetIpInfoRequest,
                    handlerOfIpInfoQuery: handlerOfIpInfoQuery
            )


    def setup() {

    }


    def "test execute"() {
        given:
        legalDocumentConfigService.getLegalDocumentConfig() >> new LegalDocumentConfig(legalDocumentList: [null])

        when:
        LegalDocumentQueryResponseVO result = processorOflegalDocumentQuery.execute(null)

        then:
        result == null
    }


}