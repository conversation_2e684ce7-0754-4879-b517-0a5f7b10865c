package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfAnnouncementQuery
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfAnnouncementQueryRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfAnnouncementQueryResponseVO
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfGetCityBaseInfoRequest
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import mockit.Mock
import mockit.MockUp
import spock.lang.Specification

class ProcessorOfAnnouncementQueryTest extends Specification {

    def mapperOfGetCityBaseInfoRequest = Mock(MapperOfGetCityBaseInfoRequest)

    def handlerOfGetCityBaseInfo = Mock(HandlerOfGetCityBaseInfo)

    def mapperOfAnnouncementQueryRequest = Mock(MapperOfAnnouncementQueryRequest)

    def handlerOfAnnouncementQuery = Mock(HandlerOfAnnouncementQuery)

    def mapperOfAnnouncementQueryResponseVO = Mock(MapperOfAnnouncementQueryResponseVO)


    def processor = new ProcessorOfAnnouncementQuery(
            mapperOfGetCityBaseInfoRequest: mapperOfGetCityBaseInfoRequest,
            handlerOfGetCityBaseInfo: handlerOfGetCityBaseInfo,
            mapperOfAnnouncementQueryRequest: mapperOfAnnouncementQueryRequest,
            handlerOfAnnouncementQuery: handlerOfAnnouncementQuery,
            mapperOfAnnouncementQueryResponseVO: mapperOfAnnouncementQueryResponseVO
    )

    def "Execute with null req"() {
        expect:
        null != processor.execute(null)
    }

    def "Execute with cityIds"() {
        given:
        def req = new AnnouncementQueryRequestVO()
        req.cityIds = ["1", "2", "3"]

        when:
        new MockUp<WaitFuture>() {
            @Mock
            public Object getWithoutError() {
                return null;
            }
        }
//        mapperOfGetCityBaseInfoRequest.map(_) >> new GetCityBaseInfoRequestType()
//        handlerOfGetCityBaseInfo.handleAsync(_) >> Mock(WaitFuture)
//        mapperOfAnnouncementQueryRequest.map(_) >> new AnnouncementQueryRequestVO()
        handlerOfAnnouncementQuery.handleAsync(_) >> Mock(WaitFuture)
//        mapperOfAnnouncementQueryResponseVO.map(_) >> new AnnouncementQueryRequestVO()
        def res = processor.execute(req)

        then:
        null == res
    }
}
