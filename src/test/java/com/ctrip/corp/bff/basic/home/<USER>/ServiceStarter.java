package com.ctrip.corp.bff.basic.home.trip;

import java.awt.*;
import java.net.URI;

import org.springframework.boot.SpringApplication;

import com.ctrip.corp.bff.framework.template.service.run.SpringBootServiceInitializer;

/**
 * <AUTHOR>
 * @date 2024/5/16 21:40
 */
public class ServiceStarter {

    public static void main(String[] args) throws Exception {
        System.setProperty("java.awt.headless", "false");
        // 本地模式请参考 http://conf.ctripcorp.com/pages/viewpage.action?pageId=192829007
        SpringApplication.run(SpringBootServiceInitializer.class);

        // port 8080 is configured in src/test/resources/application.properties(key: server.port)
        Desktop.getDesktop().browse(new URI("http://127.0.0.1:8080/api"));
    }

}
