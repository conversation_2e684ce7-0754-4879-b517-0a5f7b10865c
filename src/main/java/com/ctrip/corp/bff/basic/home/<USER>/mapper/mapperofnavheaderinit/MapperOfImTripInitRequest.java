package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/9 17:21
 * @Version 1.0
 */
@Component
public class MapperOfImTripInitRequest extends AbstractMapper<Tuple1<NavHeaderInitRequestVO>, ImTripInitRequestType> {
    @Override
    protected ImTripInitRequestType convert(Tuple1<NavHeaderInitRequestVO> tuple) {
        NavHeaderInitRequestVO navHeaderInitRequestVO = tuple.getT1();
        ImTripInitRequestType imTripInitRequest = new ImTripInitRequestType();
        imTripInitRequest.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(navHeaderInitRequestVO.getRequestHeader()));
        imTripInitRequest.setStrategyInfos(buildStrategyInfo(navHeaderInitRequestVO));
        return imTripInitRequest;
    }

    private List<StrategyInfo> buildStrategyInfo(NavHeaderInitRequestVO navHeaderInitRequestVO) {
        List<StrategyInfo> strategyInfos = new ArrayList<>();
        StrategyInfo strategyInfoProduct = new StrategyInfo();
        strategyInfoProduct.setStrategyKey("PRODUCT");
        strategyInfoProduct.setStrategyValue(navHeaderInitRequestVO.getProductLine());
        strategyInfos.add(strategyInfoProduct);

        StrategyInfo strategyInfoScene = new StrategyInfo();
        strategyInfoScene.setStrategyKey("SCENE");
        strategyInfoScene.setStrategyValue(StringUtil.isBlank(navHeaderInitRequestVO.getProductLine()) ? "HEADER" : "PRODUCT");
        strategyInfos.add(strategyInfoScene);

        return strategyInfos;
    }

    @Override
    protected ParamCheckResult check(Tuple1<NavHeaderInitRequestVO> tuple) {
        return null;
    }
}
