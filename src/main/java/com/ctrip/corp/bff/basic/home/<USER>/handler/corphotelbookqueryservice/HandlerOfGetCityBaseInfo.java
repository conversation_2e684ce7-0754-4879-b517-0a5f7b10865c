package com.ctrip.corp.bff.basic.home.trip.handler.corphotelbookqueryservice;

import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.service.CorpHotelBookQueryServiceClient;

/**
 * <AUTHOR>
 * @Date 2024/4/1 15:16
 * @Version 1.0
 */
@Component
public class HandlerOfGetCityBaseInfo extends AbstractHandlerOfSOA<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType, CorpHotelBookQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "getCityBaseInfo";
    }
}
