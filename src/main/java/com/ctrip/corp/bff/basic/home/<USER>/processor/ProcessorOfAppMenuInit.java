package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType;
import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfCountInfoQueryRequestType;
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfQueryApproveTaskRequestType;
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient.HandlerOfQueryApproveTask;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfCountInfoQuery;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappmenuinit.MapperOfAppMenuInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfProductInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuConfig;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class ProcessorOfAppMenuInit extends AbstractProcessor<AppMenuInitRequestVO, AppMenuInitResponseVO> {

    @Autowired
    private HandlerOfBrandInfoInit handlerOfBrandInfoInit;
    @Autowired
    private MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest;

    @Autowired
    private HandlerOfProductInfoInit handlerOfProductInfoInit;
    @Autowired
    private MapperOfProductInfoInitRequest mapperOfProductInfoInitRequest;

    @Autowired
    private HandlerOfCustomGray handlerOfCustomGray;
    @Autowired
    private MapperOfCustomGrayRequest mapperOfCustomGrayRequest;

    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private MapperofSoaGeneralSearchAccountInfoRequest mapperofSoaGeneralSearchAccountInfoRequest;

    @Autowired
    private HandlerOfQueryApproveTask handlerOfQueryApproveTask;
    @Autowired
    private MapperOfQueryApproveTaskRequestType mapperOfQueryApproveTaskRequestType;

    @Autowired
    private HandlerOfCountInfoQuery handlerOfCountInfoQuery;
    @Autowired
    private MapperOfCountInfoQueryRequestType mapperOfCountInfoQueryRequestType;

    @Autowired
    private MapperOfAppMenuInitResponseVO mapperOfAppMenuInitResponseVO;

    @QConfig("appMenuConfig.json")
    private AppMenuConfig appMenuConfig;

    @Override
    public AppMenuInitResponseVO execute(AppMenuInitRequestVO request) throws Exception {
        // 1、logo
        BrandInfoInitRequestType brandInfoInitRequestType =
                mapperOfBrandInfoInitRequest.map(Tuple2.of(request.getRequestHeader(), null));
        WaitFuture<BrandInfoInitRequestType, BrandInfoInitResponseType> brandInfoInitWaitFuture =
                handlerOfBrandInfoInit.handleAsync(brandInfoInitRequestType);
        // 2、产线信息
        ProductInfoInitRequestType productInfoInitRequestType =
                mapperOfProductInfoInitRequest.map(Tuple1.of(request.getRequestHeader()));
        WaitFuture<ProductInfoInitRequestType, ProductInfoInitResponseType> productInfoInitWaitFuture =
                handlerOfProductInfoInit.handleAsync(productInfoInitRequestType);
        // 3、灰度信息
        CustomGrayRequestType customGrayRequestType =
                mapperOfCustomGrayRequest.map(Tuple2.of(request.getRequestHeader(),
                        Lists.newArrayList(BIZ_APPROVAL_ENDORSEMENT_SWITCH, APPROVAL_PERMISSION_NEW)));
        WaitFuture<CustomGrayRequestType, CustomGrayResponseType> customGrayWaitFuture =
                handlerOfCustomGray.handleAsync(customGrayRequestType);
        // 4、账户信息
        GeneralSearchAccountInfoRequestType generalSearchAccountInfoRequestType =
                mapperofSoaGeneralSearchAccountInfoRequest.map(Tuple1.of(request.getRequestHeader().getHeader().getUserId()));
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> generalSearchAccountInfoWaitFuture =
                handlerOfGeneralSearchAccountInfo.handleAsync(generalSearchAccountInfoRequestType);
        // 5、审批权限
        QueryApproveTaskRequestType queryApproveTaskRequestType =
                mapperOfQueryApproveTaskRequestType.map(Tuple1.of(request.getRequestHeader()));
        WaitFuture<QueryApproveTaskRequestType, QueryApproveTaskResponseType> queryApproveTaskTypeWaitFuture =
                handlerOfQueryApproveTask.handleAsync(queryApproveTaskRequestType);
        // 6、未读消息和待审批数量
        CountInfoQueryRequestType countInfoQueryRequestType =
                mapperOfCountInfoQueryRequestType.map(Tuple2.of(request.getRequestHeader(),
                        Lists.newArrayList(UNREAD_MESSAGE, NOT_APPROVAL_NUM)));
        WaitFuture<CountInfoQueryRequestType, CountInfoQueryResponseType> countInfoQueryWaitFuture =
                handlerOfCountInfoQuery.handleAsync(countInfoQueryRequestType);

        return mapperOfAppMenuInitResponseVO.map(Tuple7.of(brandInfoInitWaitFuture.getWithoutError(),
                productInfoInitWaitFuture.getWithoutError(), customGrayWaitFuture.getWithoutError(),
                generalSearchAccountInfoWaitFuture.getWithoutError(), queryApproveTaskTypeWaitFuture.getWithoutError(),
                countInfoQueryWaitFuture.getWithoutError(), appMenuConfig));
    }

    @Override
    public Map<String, String> tracking(AppMenuInitRequestVO request, AppMenuInitResponseVO response) {
        return null;
    }
}
