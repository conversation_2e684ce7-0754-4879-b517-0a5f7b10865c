package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfNavHeaderInit;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: z.c. wang
 * @Description 导航头
 * @Date: 2024/11/20 15:22
 * @Version 1.0
 */
@WebService(name = "navHeaderInit")
public class ServiceOfNavHeaderInit extends AbstractSyncService<NavHeaderInitRequestVO, NavHeaderInitResponseVO> {
    @Autowired
    private ProcessorOfNavHeaderInit processorOfNavHeaderInit;

    @Override
    public void validateRequest(NavHeaderInitRequestVO navHeaderInitRequestVO) throws BusinessException {

    }

    @Override
    protected Processor<NavHeaderInitRequestVO, NavHeaderInitResponseVO> getProcessor(NavHeaderInitRequestVO navHeaderInitRequestVO) {
        return processorOfNavHeaderInit;
    }
}
