package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpsettlementsettingsuncoresvcapplicationclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.settlement.settings.uncore.application.CorpSettlementSettingsUnCoreSvcServiceClient;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionRequestType;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/6 20:15
 * @Version 1.0
 */
@Component
public class HandlerOfCheckCustomerCbkPermission
        extends AbstractHandlerOfSOA<CheckCustomerCbkPermissionRequestType, CheckCustomerCbkPermissionResponseType, CorpSettlementSettingsUnCoreSvcServiceClient> {
    @Override
    protected String getMethodName() {
        return "checkCustomerCbkPermission";
    }
}
