package com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
public class AppMenuConfig {

    private List<AppMenuGroupInfo> groupList;

    private List<AppMenuInfo> menuList;

    public List<AppMenuGroupInfo> getGroupList() {
        return groupList;
    }

    public void setGroupList(List<AppMenuGroupInfo> groupList) {
        this.groupList = groupList;
    }

    public List<AppMenuInfo> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<AppMenuInfo> menuList) {
        this.menuList = menuList;
    }

    public void sort() {
        if (CollectionUtils.isEmpty(groupList)) {
            groupList = Lists.newArrayList();
        }
        for (AppMenuGroupInfo appMenuGroupInfo : groupList) {
            if (appMenuGroupInfo != null && appMenuGroupInfo.getSort() == null) {
                appMenuGroupInfo.setSort(Integer.MAX_VALUE);
            }
        }
        groupList = groupList.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(AppMenuGroupInfo::getSort)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(menuList)) {
            menuList = Lists.newArrayList();
        }
        for (AppMenuInfo appMenuInfo : menuList) {
            if (appMenuInfo != null && appMenuInfo.getSort() == null) {
                appMenuInfo.setSort(Integer.MAX_VALUE);
            }
        }
        menuList = menuList.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(AppMenuInfo::getSort)).collect(Collectors.toList());
    }
}
