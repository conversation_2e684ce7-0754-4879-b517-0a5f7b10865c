package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfAppHomeInit;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/2/28
 */
@WebService(name = "appHomeInit")
public class ServiceOfAppHomeInit extends AbstractSyncService<AppHomeInitRequestVO, AppHomeInitResponseVO> {

    @Autowired
    private ProcessorOfAppHomeInit processor;

    @Override
    public void validateRequest(AppHomeInitRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<AppHomeInitRequestVO, AppHomeInitResponseVO> getProcessor(AppHomeInitRequestVO request) {
        return processor;
    }
}
