package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionRequestType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/6 20:17
 * @Version 1.0
 */
@Component
public class MapperOfCheckCustomerCbkPermissionRequest extends AbstractMapper<Tuple1<TemplateSoaRequestType>, CheckCustomerCbkPermissionRequestType> {
    @Override
    protected CheckCustomerCbkPermissionRequestType convert(Tuple1<TemplateSoaRequestType> tuple) {
        TemplateSoaRequestType templateSoaRequestType = tuple.getT1();
        CheckCustomerCbkPermissionRequestType checkCustomerCbkPermissionRequestType = new CheckCustomerCbkPermissionRequestType();
        checkCustomerCbkPermissionRequestType.setUid(templateSoaRequestType.getHeader().getUserId());
        checkCustomerCbkPermissionRequestType.setCorpId(templateSoaRequestType.getHeader().getCorpId());
        return checkCustomerCbkPermissionRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple) {
        return null;
    }
}
