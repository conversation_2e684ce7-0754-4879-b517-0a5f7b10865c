package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component
public class MapperOfMyProfileMenuInitRequestType extends AbstractMapper<Tuple1<TemplateSoaRequestType>, MyProfileMenuInitRequestType> {
    @Override
    protected MyProfileMenuInitRequestType convert(Tuple1<TemplateSoaRequestType> tuple1) {
        MyProfileMenuInitRequestType myProfileMenuInitRequestType = new MyProfileMenuInitRequestType();
        myProfileMenuInitRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(tuple1.getT1()));
        myProfileMenuInitRequestType.setStrategy(buildStrategyInfo());
        return myProfileMenuInitRequestType;
    }

    private List<StrategyInfo> buildStrategyInfo() {
        List<StrategyInfo> strategyInfos = new ArrayList<>();
        StrategyInfo strategyInfoScene = new StrategyInfo();
        strategyInfoScene.setStrategyKey("SCENE");
        strategyInfoScene.setStrategyValue("HEADER");
        strategyInfos.add(strategyInfoScene);
        return strategyInfos;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple1) {
        return null;
    }
}
