package com.ctrip.corp.bff.basic.home.trip.qconfig.apphome;

import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/2
 */
public class AppHomeConfig {

    private Map<String, String> backgroundUrlMap;

    private List<AppHomeInfo> productTabList;

    private List<AppHomeInfo> homeButtonList;

    private static final String ALL = "_ALL";
    private static final String HALF = "_HALF";
    private static final String ONLY_JOURNEY = "_ONLY_JOURNEY";

    public String getBackgroundUrlAllByPos(PosEnum posEnum) {
        if (MapUtils.isEmpty(backgroundUrlMap)) {
            return StringUtils.EMPTY;
        }
        if (backgroundUrlMap.containsKey(posEnum.getValue() + ALL)) {
            return backgroundUrlMap.get(posEnum.getValue() + ALL);
        }
        if (backgroundUrlMap.containsKey(CommonConstant.DEFAULT + ALL)) {
            return backgroundUrlMap.get(CommonConstant.DEFAULT + ALL);
        }
        return StringUtils.EMPTY;
    }

    public String getBackgroundUrlHalfByPos(PosEnum posEnum) {
        if (MapUtils.isEmpty(backgroundUrlMap)) {
            return StringUtils.EMPTY;
        }
        if (backgroundUrlMap.containsKey(posEnum.getValue() + HALF)) {
            return backgroundUrlMap.get(posEnum.getValue() + HALF);
        }
        if (backgroundUrlMap.containsKey(CommonConstant.DEFAULT + HALF)) {
            return backgroundUrlMap.get(CommonConstant.DEFAULT + HALF);
        }
        return StringUtils.EMPTY;
    }

    public String getBackgroundUrlOnlyJourneyByPos(PosEnum posEnum) {
        if (MapUtils.isEmpty(backgroundUrlMap)) {
            return StringUtils.EMPTY;
        }
        if (backgroundUrlMap.containsKey(posEnum.getValue() + ONLY_JOURNEY)) {
            return backgroundUrlMap.get(posEnum.getValue() + ONLY_JOURNEY);
        }
        if (backgroundUrlMap.containsKey(CommonConstant.DEFAULT + ONLY_JOURNEY)) {
            return backgroundUrlMap.get(CommonConstant.DEFAULT + ONLY_JOURNEY);
        }
        return StringUtils.EMPTY;
    }

    public void sort() {
        if (CollectionUtils.isEmpty(productTabList)) {
            productTabList = Lists.newArrayList();
        }
        for (AppHomeInfo appHomeInfo : productTabList) {
            if (appHomeInfo != null && appHomeInfo.getSort() == null) {
                appHomeInfo.setSort(Integer.MAX_VALUE);
            }
        }
        productTabList = productTabList.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(AppHomeInfo::getSort)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(homeButtonList)) {
            homeButtonList = Lists.newArrayList();
        }
        for (AppHomeInfo appHomeInfo : homeButtonList) {
            if (appHomeInfo != null && appHomeInfo.getSort() == null) {
                appHomeInfo.setSort(Integer.MAX_VALUE);
            }
        }
        homeButtonList = homeButtonList.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(AppHomeInfo::getSort)).collect(Collectors.toList());
    }

    public Map<String, String> getBackgroundUrlMap() {
        return backgroundUrlMap;
    }

    public void setBackgroundUrlMap(Map<String, String> backgroundUrlMap) {
        this.backgroundUrlMap = backgroundUrlMap;
    }

    public List<AppHomeInfo> getProductTabList() {
        return productTabList;
    }

    public void setProductTabList(List<AppHomeInfo> productTabList) {
        this.productTabList = productTabList;
    }

    public List<AppHomeInfo> getHomeButtonList() {
        return homeButtonList;
    }

    public void setHomeButtonList(List<AppHomeInfo> homeButtonList) {
        this.homeButtonList = homeButtonList;
    }
}
