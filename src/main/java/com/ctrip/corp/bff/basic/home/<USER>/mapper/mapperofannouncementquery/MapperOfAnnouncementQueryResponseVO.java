package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.basic.home.contract.AnnouncementDetail;
import com.ctrip.corp.bff.basic.home.contract.AnnouncementQueryResponseType;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryResponseVO;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

/**
 * <AUTHOR>
 * @date 2024/5/24 16:12
 */

@Component
public class MapperOfAnnouncementQueryResponseVO
    extends AbstractMapper<Tuple1<AnnouncementQueryResponseType>, AnnouncementQueryResponseVO> {

    @Override
    protected AnnouncementQueryResponseVO convert(Tuple1<AnnouncementQueryResponseType> tuple) {
        AnnouncementQueryResponseVO result = new AnnouncementQueryResponseVO();
        AnnouncementQueryResponseType announcementQueryResponse = tuple.getT1();
        if (Objects.isNull(announcementQueryResponse)) {
            return result;
        }
        result.setAnnouncementInfos(buildAnnouncementInfos(announcementQueryResponse.getAnnouncementDetails()));
        return result;
    }

    protected List<AnnouncementInfoVO> buildAnnouncementInfos(List<AnnouncementDetail> announcementDetails) {
        if (CollectionUtil.isEmpty(announcementDetails)) {
            return null;
        }
        List<AnnouncementInfoVO> resultList = new ArrayList<>();
        for (AnnouncementDetail announcementInfo : announcementDetails) {
            if (Objects.isNull(announcementInfo)) {
                continue;
            }
            AnnouncementInfoVO announcementInfoVO = new AnnouncementInfoVO();
            announcementInfoVO.setId(announcementInfo.getId());
            announcementInfoVO.setLevel(announcementInfo.getLevel());
            announcementInfoVO.setPopFrequency(announcementInfo.getPopFrequency());
            announcementInfoVO.setShowBulletinBoard(announcementInfo.getShowBulletinBoard());

            announcementInfoVO.setContent(announcementInfo.getContent());
            announcementInfoVO.setTitle(announcementInfo.getTitle());
            announcementInfoVO.setSubTitle(announcementInfo.getSubTitle());
            announcementInfoVO.setFormatContent(announcementInfo.getFormatContent());
            announcementInfoVO.setLink(announcementInfo.getLink());

            resultList.add(announcementInfoVO);
        }
        return resultList;
    }

    @Override
    protected ParamCheckResult check(Tuple1<AnnouncementQueryResponseType> tuple) {
        return null;
    }
}
