package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOflegalDocumentQuery;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@WebService(name = "legalDocumentQuery")
public class ServiceOfLegalDocumentQuery extends AbstractSyncService<LegalDocumentQueryRequestVO, LegalDocumentQueryResponseVO> {

    @Autowired
    private ProcessorOflegalDocumentQuery processor;

    @Override
    public void validateRequest(LegalDocumentQueryRequestVO requestType) throws BusinessException {
    }

    @Override
    protected Processor<LegalDocumentQueryRequestVO, LegalDocumentQueryResponseVO> getProcessor(LegalDocumentQueryRequestVO requestType) {
        return processor;
    }
}
