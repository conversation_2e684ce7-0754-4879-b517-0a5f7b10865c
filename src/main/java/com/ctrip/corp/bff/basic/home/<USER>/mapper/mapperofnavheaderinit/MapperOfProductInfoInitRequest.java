package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/19 16:16
 * @Version 1.0
 */
@Component
public class MapperOfProductInfoInitRequest extends AbstractMapper<Tuple1<TemplateSoaRequestType>, ProductInfoInitRequestType> {
    @Override
    protected ProductInfoInitRequestType convert(Tuple1<TemplateSoaRequestType> tuple) {
        TemplateSoaRequestType templateSoaRequestType = tuple.getT1();
        ProductInfoInitRequestType productInfoInitRequestType = new ProductInfoInitRequestType();
        productInfoInitRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(templateSoaRequestType));
        return productInfoInitRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple) {
        return null;
    }
}
