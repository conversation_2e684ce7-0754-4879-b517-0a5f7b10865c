package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.basic.home.contract.AnnouncementQueryRequestType;
import com.ctrip.corp.bff.basic.home.contract.FlightCondition;
import com.ctrip.corp.bff.basic.home.contract.FlightConditionCityInfo;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.FlightConditionCityInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.FlightConditionVO;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.vo.CorpPayTypeInfoInputVO;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.CommonBaseEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;


/**
 * <AUTHOR>
 * @date 2024/5/16 21:40
 */
@Component
public class MapperOfAnnouncementQueryRequest
    extends AbstractMapper<Tuple2<AnnouncementQueryRequestVO, GetCityBaseInfoResponseType>, AnnouncementQueryRequestType> {

    @Override
    protected AnnouncementQueryRequestType convert(Tuple2<AnnouncementQueryRequestVO, GetCityBaseInfoResponseType> tuple) {

        AnnouncementQueryRequestVO request = tuple.getT1();
        AnnouncementQueryRequestType result = new AnnouncementQueryRequestType();
        result.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(request.getRequestHeader()));
        result.setAnnouncementType(request.getAnnouncementType());
        result.setCorpPayInfo(buildCorpPayInfo(request.getCorpPayTypeInfoInput()));
        result.setPageType(buildPageType(request.getPageType()));
        result.setProductType(request.getProductType());
        result.setFlightConditions(buildFlightConditions(request.getFlightConditions()));
        result.setCityInfos(buildCityInfos(tuple.getT2()));
        return result;
    }

    protected List<CityInput> buildCityInfos(GetCityBaseInfoResponseType cityBaseResp) {
        return Optional.ofNullable(cityBaseResp).map(GetCityBaseInfoResponseType::getCityBaseInfo).orElse(new ArrayList<>()).stream().map(CityBaseInfoEntity::getCountryInfo)
            .map(CommonBaseEntity::getId).filter(Objects::nonNull).distinct().map(countryId -> new CityInput() {
                {
                    setCountryId(countryId);
                }
            }).collect(Collectors.toList());
    }

    protected String buildPageType(String pageTypeReq) {
        if (StringUtil.isBlank(pageTypeReq)) {
            return null;
        }
        return pageTypeReq;
    }

    protected CorpPayInfo buildCorpPayInfo(CorpPayTypeInfoInputVO corpPayTypeInfoInput) {
        if (Objects.isNull(corpPayTypeInfoInput)) {
            return null;
        }
        String corpPayType = corpPayTypeInfoInput.getCorpPayType();
        if (StringUtil.isBlank(corpPayType)) {
            return null;
        }
        CorpPayInfo corpPayInfo = new CorpPayInfo();
        corpPayInfo.setCorpPayType(corpPayType);
        return corpPayInfo;

    }

    protected List<FlightCondition> buildFlightConditions(List<FlightConditionVO> flightConditionsVOs){
        if (CollectionUtil.isEmpty(flightConditionsVOs)){
            return null;
        }
        List<FlightCondition> flightConditions = new ArrayList<>();
        flightConditionsVOs.stream().filter(Objects::nonNull).forEach(flightConditionVO ->{

            FlightCondition flightConditionTmp = new FlightCondition();
            FlightConditionCityInfo flightConditionCityInfoTmp = new FlightConditionCityInfo();

            FlightConditionCityInfoVO flightConditionCityInfo = flightConditionVO.getFlightConditionCityInfo();

            flightConditionCityInfoTmp.setDeparture(flightConditionCityInfo.getDeparture());
            flightConditionCityInfoTmp.setArrival(flightConditionCityInfo.getArrival());
            flightConditionCityInfoTmp.setTransit(flightConditionCityInfo.getTransit());
            flightConditionCityInfoTmp.setStopping(flightConditionCityInfo.getStopping());

            flightConditionTmp.setFlightConditionCityInfo(flightConditionCityInfoTmp);

            flightConditions.add(flightConditionTmp);
        });
        
        return flightConditions;
    }

    @Override
    protected ParamCheckResult check(Tuple2<AnnouncementQueryRequestVO, GetCityBaseInfoResponseType> tuple) {
        return null;
    }
}
