package com.ctrip.corp.bff.basic.home.trip.common.config;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Date: 2024/3/21 10:11
 */
public class CopyrightConfig {

    /**
     * 默认版权
     */
    private String defaultCopyright;

    /**
     * 签约主体->版权 映射
     */
    private Map<String, String> agreementSignCompanyCopyright;

    /**
     * gatewayHoet->版权 映射
     */
    private Map<String, String> loginGatewayCopyright;

    public String getDefaultCopyright() {
        return defaultCopyright;
    }

    public void setDefaultCopyright(String defaultCopyright) {
        this.defaultCopyright = defaultCopyright;
    }

    public Map<String, String> getAgreementSignCompanyCopyright() {
        return agreementSignCompanyCopyright;
    }

    public void setAgreementSignCompanyCopyright(Map<String, String> agreementSignCompanyCopyright) {
        this.agreementSignCompanyCopyright = agreementSignCompanyCopyright;
    }

    public Map<String, String> getLoginGatewayCopyright() {
        return loginGatewayCopyright;
    }

    public void setLoginGatewayCopyright(Map<String, String> loginGatewayCopyright) {
        this.loginGatewayCopyright = loginGatewayCopyright;
    }

    /**
     * 签约主体copyright
     *
     * @param agreementSignCompany
     * @return
     */
    public String getCopyrightWithDefault(String agreementSignCompany, String gatewayHost) {
        // 临时方案，tyo-masters要使用指定的copyright
        if (StringUtil.equalsIgnoreCase("www.tyo-masters.co.jp", gatewayHost)) {
            return loginGatewayCopyright.get(gatewayHost);
        }

        if (agreementSignCompanyCopyright == null || StringUtil.isBlank(agreementSignCompany)) {
            return defaultCopyright;
        }
        String copyright = agreementSignCompanyCopyright.get(agreementSignCompany);
        if (StringUtil.isNotBlank(copyright)) {
            return copyright;
        }
        return defaultCopyright;
    }

    /**
     * 登录页copyright
     *
     * @param gatewayHost
     * @return
     */
    public String getLoginCopyrightWithDefault(String gatewayHost) {
        if (loginGatewayCopyright == null || StringUtil.isBlank(gatewayHost)) {
            return defaultCopyright;
        }
        String copyright = loginGatewayCopyright.get(gatewayHost);
        if (StringUtil.isNotBlank(copyright)) {
            return copyright;
        }
        return defaultCopyright;
    }
}
