package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadContextUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;

import java.text.MessageFormat;
import java.util.Map;

/**
 * @Author: z.c. wang
 * @Description 导航头admin tool菜单信息
 * @Date: 2024/11/20 16:24
 * @Version 1.0
 */
public enum AdminToolEnum implements NavHeaderEnumInterface {

    /**
     * Reporting
     */
    REPORTING("REPORTING", null, GroupEnum.REPORTING, "reportingUrl"),

    /**
     * Approval Policy Setting
     */
    APPROVAL_POLICY_SETTING("APPROVAL_POLICY_SETTING", null, GroupEnum.SETTING, "approvalPolicySettingUrl"),

    /**
     * Travel Policy Setting
     */
    TRAVEL_POLICY_SETTING("TRAVEL_POLICY_SETTING", null, GroupEnum.SETTING, "travelPolicySettingUrl"),

    /**
     * User Management
     */
    USER_MANAGEMENT("USER_MANAGEMENT", null, GroupEnum.MANAGEMENT, "userManagementUrl"),

    /**
     * Booking Management
     */
    BOOKING_MANAGEMENT("BOOKING_MANAGEMENT", null, GroupEnum.MANAGEMENT, "bookingManagementUrl"),

    /**
     * Company Payment
     */
    COMPANY_PAYMENT("COMPANY_PAYMENT", null, GroupEnum.MANAGEMENT, "companyPaymentUrl"),

    /**
     * Settlement Management
     */
    SETTLEMENT_MANAGEMENT("SETTLEMENT_MANAGEMENT", null, GroupEnum.MANAGEMENT, "settlementManagementUrl"),
    /**
     * Travel Request Management
     */
    TRAVEL_REQUEST_MANAGEMENT("TRAVEL_REQUEST_MANAGEMENT", null, GroupEnum.MANAGEMENT, "travelRequestManagementUrl"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 图标
     */
    private final String icon;

    /**
     * 分组code
     */
    private final GroupEnum group;

    /**
     * 链接
     */
    private final String url;

    AdminToolEnum(String code, String icon, GroupEnum group, String url) {
        this.code = code;
        this.icon = icon;
        this.group = group;
        this.url = url;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public GroupEnum getGroup() {
        return group;
    }

    @Override
    public String getName() {
        return BFFSharkUtil.getSharkValue("trip.biz.common.biz.text.header.admin_tool." + name().toLowerCase());
    }

    @Override
    public String getUrl(Map<String, String> navHeaderUrlConfig) {
        if (MapUtil.isEmpty(navHeaderUrlConfig)) {
            return "";
        }
        if (StringUtil.equalsIgnoreCase(url, SETTLEMENT_MANAGEMENT.url)) {
            return MessageFormat.format(navHeaderUrlConfig.get(url), "BLUE_SPACE", ThreadLocalProvider.getOrCreate().getLanguage());
        }
        return navHeaderUrlConfig.get(url);
    }

}
