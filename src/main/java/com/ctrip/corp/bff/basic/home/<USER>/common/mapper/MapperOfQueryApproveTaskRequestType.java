package com.ctrip.corp.bff.basic.home.trip.common.mapper;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.framework.foundation.Foundation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MapperOfQueryApproveTaskRequestType extends AbstractMapper<Tuple1<TemplateSoaRequestType>, QueryApproveTaskRequestType> {
    @Override
    protected QueryApproveTaskRequestType convert(Tuple1<TemplateSoaRequestType> tuple) {
        QueryApproveTaskRequestType requestType = new QueryApproveTaskRequestType();
        requestType.setServerFrom(Foundation.app().getAppId());
        requestType.setSessionId(getRequestId(tuple.getT1()));
        requestType.setCompanyId(getCorpId(tuple.getT1()));
        requestType.setUid(getUserId(tuple.getT1()));
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple) {
        return null;
    }

    private String getRequestId(TemplateSoaRequestType request) {
        return Optional.ofNullable(request)
                .map(TemplateSoaRequestType::getRequestId)
                .orElse(UUID.randomUUID().toString());
    }

    private String getCorpId(TemplateSoaRequestType request) {
        return Optional.ofNullable(request)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getCorpId)
                .orElse(StringUtils.EMPTY);
    }

    private String getUserId(TemplateSoaRequestType request) {
        return Optional.ofNullable(request)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getUserId)
                .orElse(StringUtils.EMPTY);
    }

}