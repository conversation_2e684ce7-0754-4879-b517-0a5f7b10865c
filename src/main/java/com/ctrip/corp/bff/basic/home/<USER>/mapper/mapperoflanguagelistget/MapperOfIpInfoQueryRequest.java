package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.tools.contract.IpInfoQueryRequestType;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * @Description
 * @author: renyiwang
 * @Date: 2024/11/6
 */
@Component
public class MapperOfIpInfoQueryRequest extends AbstractMapper<Tuple1<TemplateSoaRequestType>, IpInfoQueryRequestType> {
    @Override
    protected IpInfoQueryRequestType convert(Tuple1<TemplateSoaRequestType> templateSoaRequestTypeTuple1) {
        if (Objects.isNull(templateSoaRequestTypeTuple1.getT1())) {
            return null;
        }
        IpInfoQueryRequestType ipInfoQueryRequestType = new IpInfoQueryRequestType();
        ipInfoQueryRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(templateSoaRequestTypeTuple1.getT1()));
        return ipInfoQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> templateSoaRequestTypeTuple1) {
        return null;
    }
}
