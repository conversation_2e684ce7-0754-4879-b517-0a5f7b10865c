package com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument;

import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 法务文档配置 http://conf.ctripcorp.com/pages/viewpage.action?pageId=2175041745
 * @author:  mmt
 * @Date: 2024/8/6
 */
@Service
public class LegalDocumentConfigService {

    @QConfig("LegalDocumentConfig.json")
    private LegalDocumentConfig legalDocumentConfig;

    public LegalDocumentConfig getLegalDocumentConfig() {
        Map<String, String> tagMap = new HashMap<>();
        LogUtil.loggingClogOnly(LogLevelEnum.Info, LegalDocumentConfigService.class, "getLegalDocumentConfig",
            JsonUtil.toJson(legalDocumentConfig), tagMap);
        return legalDocumentConfig;
    }

    public void setLegalDocumentConfig(LegalDocumentConfig legalDocumentConfig) {
        this.legalDocumentConfig = legalDocumentConfig;
    }
}
