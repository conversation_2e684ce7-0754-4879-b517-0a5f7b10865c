package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfGetCorpUserInfoRequest;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> mmt
 * @date 2024/5/22 10:47
 */
@Component
public class ProcessorOfLanguageListQuery extends AbstractProcessor<LanguageListQueryRequestVO, LanguageListQueryResponseVO> {

    @Autowired
    private HandlerOfGetCorpInfo handlerOfGetCorpInfo;

    @Autowired
    private MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest;

    @Autowired
    private MapperOfLanguageList mapperOfLanguageList;

    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;


    @Autowired
    private MapperofSoaGeneralSearchAccountInfoRequest mapperofSoaGeneralSearchAccountInfoRequest;


    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;


    @Override
    public LanguageListQueryResponseVO execute(LanguageListQueryRequestVO request) {

        String userId = Optional.ofNullable(request)
                .map(LanguageListQueryRequestVO::getRequestHeader)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getUserId)
                .orElse(null);
        String corpId = Optional.ofNullable(request)
                .map(LanguageListQueryRequestVO::getRequestHeader)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getCorpId)
                .orElse(null);
        GetCorpInfoResponseType getCorpInfoResponseType = null;
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = null;
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = null;
        // 有登录态场景
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(corpId)) {
            // 用户信息
            WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoWaitFuture =
                handlerOfGetCorpUserInfo.handleAsync(
                    mapperOfGetCorpUserInfoRequest.map(Tuple1.of(request.getRequestHeader())));

            GetCorpInfoRequestType getCorpInfoRequestType = mapperOfSoaGetCorpInfoRequest.map(Tuple1.of(corpId));
            getCorpInfoResponseType = handlerOfGetCorpInfo.handleAsync(getCorpInfoRequestType).get();
            GeneralSearchAccountInfoRequestType generalSearchAccountInfoRequestType = mapperofSoaGeneralSearchAccountInfoRequest
                    .map(Tuple1.of(userId));
            generalSearchAccountInfoResponseType = handlerOfGeneralSearchAccountInfo
                    .handleAsync(generalSearchAccountInfoRequestType).get();
            getCorpUserInfoResponseType = getCorpUserInfoWaitFuture.getWithoutError();
        }
        LanguageListQueryResponseVO response = new LanguageListQueryResponseVO();
        response.setLanguageInfoList(mapperOfLanguageList.map(
            Tuple5.of(Optional.ofNullable(request).map(LanguageListQueryRequestVO::getRequestHeader).orElse(null),
                getCorpInfoResponseType, generalSearchAccountInfoResponseType, request.getScene(),
                getCorpUserInfoResponseType)));

        return response;
    }


    @Override
    public Map<String, String> tracking(LanguageListQueryRequestVO request, LanguageListQueryResponseVO response) {
        return null;
    }
}
