package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CorpBffToolsServiceClient;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
@Component
public class HandlerOfCustomGray
    extends AbstractHandlerOfSOA<CustomGrayRequestType, CustomGrayResponseType, CorpBffToolsServiceClient> {
    @Override
    protected String getMethodName() {
        return "customGray";
    }
}
