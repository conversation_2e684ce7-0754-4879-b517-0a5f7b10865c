package com.ctrip.corp.bff.basic.home.trip.common.enums;

/**
 * @Description 法务文档相关产品类型
 * @author:  mmt
 * @Date: 2024/8/6
 */
public enum DocumentProductEnum {

    /**
     * 隐私条款
     */
    PRIVACY_STATEMENT("privacyStatement"),
    /**
     * 法务条款
     */
    TERMS_AND_CONDITIONS("termsAndConditions"),

    ;

    private String code;

    DocumentProductEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
