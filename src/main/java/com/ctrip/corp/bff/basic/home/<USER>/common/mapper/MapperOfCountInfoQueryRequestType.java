package com.ctrip.corp.bff.basic.home.trip.common.mapper;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MapperOfCountInfoQueryRequestType extends AbstractMapper<Tuple2<TemplateSoaRequestType, List<String>>, CountInfoQueryRequestType> {
    @Override
    protected CountInfoQueryRequestType convert(Tuple2<TemplateSoaRequestType, List<String>> tuple) {
        CountInfoQueryRequestType requestType = new CountInfoQueryRequestType();
        requestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(tuple.getT1()));
        requestType.setQueryKeys(tuple.getT2());
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, List<String>> tuple) {
        return null;
    }
}
