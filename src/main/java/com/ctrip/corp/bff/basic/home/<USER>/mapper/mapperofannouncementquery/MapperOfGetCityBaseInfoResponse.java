package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.CommonBaseEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;

/**
 * <AUTHOR>
 * @date: 2024/10/17
 */
@Component
public class MapperOfGetCityBaseInfoResponse extends AbstractMapper<Tuple1<GetCityBaseInfoResponseType>, List<String>> {

    @Override
    protected List<String> convert(Tuple1<GetCityBaseInfoResponseType> tuple) {

        GetCityBaseInfoResponseType getCityBaseInfoResponseType = tuple.getT1();
        if (Objects.isNull(getCityBaseInfoResponseType) || Objects.isNull(getCityBaseInfoResponseType.getCityBaseInfo())) {
            return new ArrayList<>();
        }
        List<String> countryIds =
            getCityBaseInfoResponseType.getCityBaseInfo().stream().map(CityBaseInfoEntity::getCountryInfo).map(CommonBaseEntity::getId).map(TemplateNumberUtil::toString).toList();
        return countryIds;
    }

    @Override
    protected ParamCheckResult check(Tuple1<GetCityBaseInfoResponseType> tuple) {
        return null;
    }
}
