package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfAppMenuInit;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@WebService(name = "appMenuInit")
public class ServiceOfAppMenuInit extends AbstractSyncService<AppMenuInitRequestVO, AppMenuInitResponseVO> {

    @Autowired
    private ProcessorOfAppMenuInit processor;

    @Override
    public void validateRequest(AppMenuInitRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<AppMenuInitRequestVO, AppMenuInitResponseVO> getProcessor(AppMenuInitRequestVO request) {
        return processor;
    }
}
