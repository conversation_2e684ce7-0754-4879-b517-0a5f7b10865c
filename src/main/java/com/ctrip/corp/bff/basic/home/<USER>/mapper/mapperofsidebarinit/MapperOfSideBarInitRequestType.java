package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit;

import com.ctrip.corp.bff.basic.home.contract.SideBarInitRequestType;
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitRequestVO;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
public class MapperOfSideBarInitRequestType extends AbstractMapper<Tuple1<SideBarInitRequestVO>, SideBarInitRequestType> {
    @Override
    protected SideBarInitRequestType convert(Tuple1<SideBarInitRequestVO> tuple1) {
        return buildSideBarInitRequestType(tuple1.getT1());
    }

    @Override
    protected ParamCheckResult check(Tuple1<SideBarInitRequestVO> tuple1) {
        return null;
    }

    private SideBarInitRequestType buildSideBarInitRequestType(SideBarInitRequestVO sideBarInitRequestVO) {
        SideBarInitRequestType sideBarInitRequestType = new SideBarInitRequestType();
        sideBarInitRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(sideBarInitRequestVO.getRequestHeader()));
        sideBarInitRequestType.setPageType(sideBarInitRequestVO.getPageType());
        sideBarInitRequestType.setPolicyUid(sideBarInitRequestVO.getPolicyUid());
        sideBarInitRequestType.setProductLine(sideBarInitRequestVO.getProductLine());
        return sideBarInitRequestType;
    }

}
