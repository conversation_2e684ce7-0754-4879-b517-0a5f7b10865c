package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit;

import com.ctrip.corp.bff.basic.contract.TabBarVO;
import com.ctrip.corp.bff.basic.home.contract.SideBarInfo;
import com.ctrip.corp.bff.basic.home.contract.SideBarInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitResponseVO;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
public class MapperOfSideBarInitResponseVO extends AbstractMapper<Tuple1<SideBarInitResponseType>, SideBarInitResponseVO> {
    @Override
    protected SideBarInitResponseVO convert(Tuple1<SideBarInitResponseType> tuple1) {
        return buildSideBarInitResponseVO(tuple1.getT1());
    }

    @Override
    protected ParamCheckResult check(Tuple1<SideBarInitResponseType> tuple1) {
        return null;
    }

    private SideBarInitResponseVO buildSideBarInitResponseVO(SideBarInitResponseType sideBarInitResponseType) {
        SideBarInitResponseVO sideBarInitResponseVO = new SideBarInitResponseVO();
        sideBarInitResponseVO.setSideBars(buildSideBarList(sideBarInitResponseType.getSideBarInfos()));
        return sideBarInitResponseVO;
    }

    private List<TabBarVO> buildSideBarList(List<SideBarInfo> sideBarInfoList) {
        if (CollectionUtils.isEmpty(sideBarInfoList)) {
            return Lists.newArrayList();
        }
        return sideBarInfoList.stream().filter(Objects::nonNull).map(this::buildSideBar).collect(Collectors.toList());
    }

    private TabBarVO buildSideBar(SideBarInfo sideBarInfo) {
        TabBarVO tabBarVO = new TabBarVO();
        tabBarVO.setCode(sideBarInfo.getCode());
        tabBarVO.setName(BFFSharkUtil.getSharkValue(SharkKeyConstant.SIDE_BAR_NAME + sideBarInfo.getCode()));
        tabBarVO.setRedirectUrl(sideBarInfo.getUrl());
        if (StringUtils.equalsIgnoreCase(BooleanConstant.TRUE, sideBarInfo.getUnreadMessageRemind())) {
            tabBarVO.setUnreadMessageRemind(sideBarInfo.getUnreadMessageRemind());
        }
        return tabBarVO;
    }

}
