package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.contract.GroupInfoVO;
import com.ctrip.corp.bff.basic.contract.MenuInfoVO;
import com.ctrip.corp.bff.basic.contract.MenuUnitInfoVO;
import com.ctrip.corp.bff.basic.contract.UserInfoVO;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.builder.NavHeaderInitResponse;
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant;
import com.ctrip.corp.bff.basic.home.trip.common.constant.GeneralSearchAccountInfoField;
import com.ctrip.corp.bff.basic.home.trip.common.constant.NavHeaderInitConstant;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.AdminToolEnum;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.GroupEnum;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.ManageTripEnum;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.MyProfileEnum;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.NavHeaderEnumInterface;
import com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit.PlanTripEnum;
import com.ctrip.corp.bff.basic.home.trip.contract.ImEntranceInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ImTripInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ServiceContractInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ServiceEmailInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ServicePhoneInfoVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList;
import com.ctrip.corp.bff.basic.home.trip.qconfig.payment.CardInfo;
import com.ctrip.corp.bff.basic.home.trip.qconfig.payment.PaymentAgreementSignCompanyInfoConfig;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.im.contract.ImEntranceInfo;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import com.ctrip.corp.bff.im.contract.ServiceContractInfo;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.authorityManage.CenterAuthFunctionType;
import corp.user.service.authorityManage.CenterAuthInfoType;
import corp.user.service.authorityManage.CheckAuthResponseType;
import corp.user.service.authorityManage.CheckCenterAuthDataType;
import corp.user.service.authorityManage.FunctionContentType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import corp.user.service.authorityManage.QueryCenterAuthDataType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.APPROVAL_PERMISSION_NEW;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.BLUESPACE_ADMIN;

/**
 * @Author: z.c. wang
 * @Description 导航头出参mapper
 * @Date: 2024/11/20 17:24
 * @Version 1.0
 */
@Component
public class MapperOfNavHeaderInitResponseVO extends AbstractMapper<Tuple1<NavHeaderInitResponse>, NavHeaderInitResponseVO> {
    private static final String ENDORSEMENT_TYPE = "A";
    /**
     * 账户类型-代理
     */
    private static final Short ACCOUNT_TYPE_AGENT = 8;

    @QMapConfig("navHeaderUrlConfig.properties")
    private Map<String, String> navHeaderUrlConfig;
    @Autowired
    private MapperOfLanguageList mapperOfLanguageList;

    @QConfig("*********#paymentAgreementSignCompanyInfo.json")
    private PaymentAgreementSignCompanyInfoConfig paymentAgreementSignCompanyInfoConfig;



    @Override
    protected NavHeaderInitResponseVO convert(Tuple1<NavHeaderInitResponse> tuple) {
        NavHeaderInitResponse navHeaderInitResponse = tuple.getT1();
        // 是否有登录态
        boolean checkRequest = BooleanUtil.isTrue(navHeaderInitResponse.getCheckRequest());
        // 公司信息
        GetCorpInfoResponseType getCorpInfoResponseType = navHeaderInitResponse.getGetCorpInfoResponseType();
        // 员工信息
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = navHeaderInitResponse.getGetCorpUserInfoResponseType();
        // 权限信息
        QueryAuthResponseType queryAuthResponseType = navHeaderInitResponse.getQueryAuthResponseType();
        CheckAuthResponseType checkAuthResponseType = navHeaderInitResponse.getCheckAuthResponseType();
        QueryAuthResponseType queryAuthAdminResponseType = navHeaderInitResponse.getQueryAuthAdminResponseType();
        // 账户信息
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = navHeaderInitResponse.getGeneralSearchAccountInfoResponseType();
        // 灰度
        CustomGrayResponseType customGrayResponseType = navHeaderInitResponse.getCustomGrayResponseType();
        // 产线
        ProductInfoInitResponseType productInfoInitResponseType = navHeaderInitResponse.getProductInfoInitResponseType();
        // obt结算权限
        CheckCustomerCbkPermissionResponseType checkCustomerCbkPermissionResponseType = navHeaderInitResponse.getCheckCustomerCbkPermissionResponseType();
        // 品牌信息
        BrandInfoInitResponseType brandInfoInitResponseType = navHeaderInitResponse.getBrandInfoInitResponseType();
        // 我携信息
        MyProfileMenuInitResponseType myProfileMenuInitResponseType = navHeaderInitResponse.getMyProfileMenuInitResponseType();
        // im信息
        ImTripInitResponseType imTripInitResponse = navHeaderInitResponse.getImTripInitResponse();
        // 审批权限信息
        QueryApproveTaskResponseType queryApproveTaskResponseType = navHeaderInitResponse.getQueryApproveTaskResponseType();
        NavHeaderInitResponseVO responseVO = new NavHeaderInitResponseVO();
        if (!checkRequest) {
            responseVO.setBrandInfo(MapperOfBrandInfoInitResponseVO.buildBrandInfo(Optional.ofNullable(brandInfoInitResponseType)
                    .map(BrandInfoInitResponseType::getBrandInfo).orElse(null)));
            responseVO.setLanguageInfoList(mapperOfLanguageList.map(Tuple5.of(
                    Optional.ofNullable(navHeaderInitResponse.getNavHeaderInitRequestVO()).map(NavHeaderInitRequestVO::getRequestHeader).orElse(null),
                    getCorpInfoResponseType, generalSearchAccountInfoResponseType, null, null)));
            return responseVO;
        }

        responseVO.setUserInfo(buildUserInfoVO(getCorpUserInfoResponseType));
        responseVO.setPlanTripMenuInfo(buildMenuInfoVO(buildSupportPlanTripMenus(productInfoInitResponseType)));
        responseVO.setManageTripMenuInfo(buildMenuInfoVO(buildManageTripMenuInfo(generalSearchAccountInfoResponseType,
                customGrayResponseType, queryApproveTaskResponseType)));
        responseVO.setAdminToolMenuInfo(buildMenuInfoVO(buildAdminToolMenus(getCorpInfoResponseType, getCorpUserInfoResponseType,
                checkAuthResponseType, queryAuthResponseType, checkCustomerCbkPermissionResponseType,
                queryAuthAdminResponseType, generalSearchAccountInfoResponseType, customGrayResponseType)));
        responseVO.setMyProfileMenuInfo(buildMenuInfoVO(buildMyProfileMenus(myProfileMenuInitResponseType)));
        responseVO.setLanguageInfoList(mapperOfLanguageList.map(Tuple5.of(
                Optional.ofNullable(navHeaderInitResponse.getNavHeaderInitRequestVO()).map(NavHeaderInitRequestVO::getRequestHeader).orElse(null),
                getCorpInfoResponseType, generalSearchAccountInfoResponseType, null, null)));
        responseVO.setImTripInfo(buildImTripInfoVO(imTripInitResponse));
        responseVO.setBrandInfo(MapperOfBrandInfoInitResponseVO.buildBrandInfo(Optional.ofNullable(brandInfoInitResponseType)
                .map(BrandInfoInitResponseType::getBrandInfo).orElse(null)));
        return responseVO;
    }

    private ImTripInfoVO buildImTripInfoVO(ImTripInitResponseType imTripInitResponse) {
        if (imTripInitResponse == null) {
            return null;
        }
        ImTripInfoVO imTripInfoVO = new ImTripInfoVO();
        imTripInfoVO.setImEntranceInfoList(buildImEntranceInfoList(imTripInitResponse.getImEntranceInfoList()));
        imTripInfoVO.setServiceContractInfo(buildServiceContractInfo(imTripInitResponse.getServiceContractInfo()));
        return imTripInfoVO;
    }

    private ServiceContractInfoVO buildServiceContractInfo(ServiceContractInfo serviceContractInfo) {
        if (serviceContractInfo == null) {
            return null;
        }
        ServiceContractInfoVO serviceContractInfoVO = new ServiceContractInfoVO();
        if (CollectionUtil.isNotEmpty(serviceContractInfo.getServiceEmailInfoList())) {
            serviceContractInfoVO.setServiceEmailInfoList(serviceContractInfo.getServiceEmailInfoList().stream().map(serviceEmailInfo -> {
                ServiceEmailInfoVO serviceEmailInfoVO = new ServiceEmailInfoVO();
                serviceEmailInfoVO.setEmail(serviceEmailInfo.getEmail());
                return serviceEmailInfoVO;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(serviceContractInfo.getServicePhoneInfoList())) {
            serviceContractInfoVO.setServicePhoneInfoList(serviceContractInfo.getServicePhoneInfoList().stream().map(servicePhoneInfo -> {
                ServicePhoneInfoVO servicePhoneInfoVO = new ServicePhoneInfoVO();
                servicePhoneInfoVO.setPhone(servicePhoneInfo.getPhone());
                servicePhoneInfoVO.setCountryCode(servicePhoneInfo.getCountryCode());
                servicePhoneInfoVO.setCountryName(servicePhoneInfo.getCountryName());
                return servicePhoneInfoVO;
            }).collect(Collectors.toList()));
        }
        return serviceContractInfoVO;
    }

    private List<ImEntranceInfoVO> buildImEntranceInfoList(List<ImEntranceInfo> imEntranceInfoList) {
        if (CollectionUtil.isEmpty(imEntranceInfoList)) {
            return null;
        }
        return imEntranceInfoList.stream().map(imEntranceInfo -> {
            ImEntranceInfoVO imEntranceInfoVO = new ImEntranceInfoVO();
            imEntranceInfoVO.setType(imEntranceInfo.getType());
            imEntranceInfoVO.setName(BFFSharkUtil.getSharkValue("trip.biz.bff.common.im.trip.entrance." + imEntranceInfo.getType().toLowerCase()));
            imEntranceInfoVO.setIcon(imEntranceInfo.getIcon());
            imEntranceInfoVO.setBizLine(imEntranceInfo.getBizLine());
            return imEntranceInfoVO;
        }).collect(Collectors.toList());
    }

    private List<NavHeaderEnumInterface> buildAdminToolMenus(GetCorpInfoResponseType getCorpInfoResponseType,
                                                             GetCorpUserInfoResponseType getCorpUserInfoResponseType,
                                                             CheckAuthResponseType checkAuthResponseType,
                                                             QueryAuthResponseType queryAuthResponseType,
                                                             CheckCustomerCbkPermissionResponseType checkCustomerCbkPermissionResponseType,
                                                             QueryAuthResponseType queryAuthResponseTypeAdmin,
                                                             GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType,
                                                             CustomGrayResponseType customGrayResponseType) {
        List<NavHeaderEnumInterface> navHeaderEnumInterfaces = new ArrayList<>();
        if (isReporting(queryAuthResponseType)) {
            navHeaderEnumInterfaces.add(AdminToolEnum.REPORTING);
        }
        String agreementSignCompany = Optional.ofNullable(getCorpInfoResponseType)
                .map(GetCorpInfoResponseType::getAgreementSignCompany).orElse(null);
        if (isAdmin(getCorpInfoResponseType, getCorpUserInfoResponseType, checkAuthResponseType)) {
            if (checkAuthHasPermissionUnlimited(checkAuthResponseType)) {
                navHeaderEnumInterfaces.add(AdminToolEnum.APPROVAL_POLICY_SETTING);
                navHeaderEnumInterfaces.add(AdminToolEnum.TRAVEL_POLICY_SETTING);
                navHeaderEnumInterfaces.add(AdminToolEnum.USER_MANAGEMENT);
                navHeaderEnumInterfaces.add(AdminToolEnum.BOOKING_MANAGEMENT);
                if (supportEndorsement(customGrayResponseType, generalSearchAccountInfoResponseType)
                        && hasRequestTotalAuth(queryAuthResponseTypeAdmin)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.TRAVEL_REQUEST_MANAGEMENT);
                }
                if (checkPayment(agreementSignCompany)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.COMPANY_PAYMENT);
                }
            } else if (checkAuthHasPermissionDiy(checkAuthResponseType)) {
                List<CenterAuthFunctionType> functions = checkAuthResponseType.getCenterAuthData().getFunctions();
                if (checkCurrAuthHasPermission(NavHeaderInitConstant.AUTH_CODE_APPROVAL_TOTAL, functions)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.APPROVAL_POLICY_SETTING);
                }
                if (checkCurrAuthHasPermission(NavHeaderInitConstant.AUTH_CODE_TRAVEL_TOTAL, functions)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.TRAVEL_POLICY_SETTING);
                }
                if (checkCurrAuthHasPermission(NavHeaderInitConstant.AUTH_CODE_USER_TOTAL, functions)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.USER_MANAGEMENT);
                }
                if (checkCurrAuthHasPermission(NavHeaderInitConstant.AUTH_CODE_ORDER_TOTAL, functions)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.BOOKING_MANAGEMENT);
                }
                if (supportEndorsement(customGrayResponseType, generalSearchAccountInfoResponseType)
                        && hasRequestTotalAuth(queryAuthResponseTypeAdmin)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.TRAVEL_REQUEST_MANAGEMENT);
                }
                if (checkCurrAuthHasPermission(NavHeaderInitConstant.AUTH_CODE_PTM_TOTAL, functions) && checkPayment(agreementSignCompany)) {
                    navHeaderEnumInterfaces.add(AdminToolEnum.COMPANY_PAYMENT);
                }
            }
        } else {
            if (supportEndorsement(customGrayResponseType, generalSearchAccountInfoResponseType)
                    && hasRequestTotalAuth(queryAuthResponseTypeAdmin)) {
                navHeaderEnumInterfaces.add(AdminToolEnum.TRAVEL_REQUEST_MANAGEMENT);
            }
        }
        if (Optional.ofNullable(checkCustomerCbkPermissionResponseType).map(CheckCustomerCbkPermissionResponseType::isHasPermission).orElse(false)) {
            navHeaderEnumInterfaces.add(AdminToolEnum.SETTLEMENT_MANAGEMENT);
        }
        return navHeaderEnumInterfaces;
    }

    private boolean checkPayment(String agreementSignCompany) {
        if (paymentAgreementSignCompanyInfoConfig == null) {
            return false;
        }
        if (MapUtils.isEmpty(paymentAgreementSignCompanyInfoConfig.getCompanyCardMap())) {
            return false;
        }
        if (!paymentAgreementSignCompanyInfoConfig.getCompanyCardMap().containsKey(agreementSignCompany)) {
            return false;
        }
        List<CardInfo> cardInfoList = paymentAgreementSignCompanyInfoConfig.getCompanyCardMap().getOrDefault(agreementSignCompany, Lists.newArrayList());
        return !CollectionUtils.isEmpty(cardInfoList);
    }


    private List<NavHeaderEnumInterface> buildSupportPlanTripMenus(ProductInfoInitResponseType productInfoInitResponseType) {
        if (productInfoInitResponseType == null
                || CollectionUtil.isEmpty(productInfoInitResponseType.getProductInfos())) {
            return null;
        }
        return productInfoInitResponseType.getProductInfos().stream().filter(Objects::nonNull)
                .filter(x -> StringUtil.equalsIgnoreCase("T", x.getShow()))
                .map(x -> PlanTripEnum.getByProductLine(x.getProductLine())
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<NavHeaderEnumInterface> buildMyProfileMenus(MyProfileMenuInitResponseType myProfileMenuInitResponseType) {
        if (myProfileMenuInitResponseType == null
                || CollectionUtil.isEmpty(myProfileMenuInitResponseType.getMenus())) {
            return null;
        }
        return myProfileMenuInitResponseType.getMenus().stream().filter(Objects::nonNull)
                .map(x -> MyProfileEnum.getByMyProfileCode(x.getCode())
                ).filter(Objects::nonNull).collect(Collectors.toList());
    }



    private List<NavHeaderEnumInterface> buildManageTripMenuInfo(GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType,
                                                                 CustomGrayResponseType customGrayResponseType,
                                                                 QueryApproveTaskResponseType queryApproveTaskResponseType) {
        List<NavHeaderEnumInterface> navHeaderEnumInterfaces = new ArrayList<>();
        navHeaderEnumInterfaces.add(ManageTripEnum.UPCOMING_TRIPS);
        navHeaderEnumInterfaces.add(ManageTripEnum.ALL_BOOKINGS);
        if (isItineraryModel(generalSearchAccountInfoResponseType)) {
            navHeaderEnumInterfaces.add(ManageTripEnum.ITINERARY);
        } else {
            navHeaderEnumInterfaces.add(ManageTripEnum.JOURNEYS);
        }

        if (supportEndorsement(customGrayResponseType, generalSearchAccountInfoResponseType)) {
            navHeaderEnumInterfaces.add(ManageTripEnum.TRAVEL_REQUEST);
        }
        if (supportApproval(customGrayResponseType, queryApproveTaskResponseType)) {
            navHeaderEnumInterfaces.add(ManageTripEnum.TRAVEL_APPROVAL);
        }
        return navHeaderEnumInterfaces;
    }

    /**
     * 是否无感行程模式
     * @param generalSearchAccountInfoResponseType
     * @return
     */
    public static boolean isItineraryModel(GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
        // 菜单标题无感行程是否需要区分和合并行程使用不同的文字, 因为前端要所有页面升级组件，否则新增code的icon不显示，所以做一个开关
        boolean specialMenuTitleSwitch = StringUtil.compareIgnoreCase(
                QConfigUtil.getAppFile().getConfigValue("itineraryModelSpecialMenuTitleSwitch"), BooleanConstant.TRUE);
        if (!specialMenuTitleSwitch) {
            return false;
        }

        // 无感行程的话  IsSenItinerary=F 代表无感行程 IsSenItinerary=T代表有感行程
        return generalSearchAccountInfoResponseType != null
            && CollectionUtil.isNotEmpty(generalSearchAccountInfoResponseType.getResults())
            && StringUtil.containsIgnoreCase(
            MapUtil.get(generalSearchAccountInfoResponseType.getResults(), GeneralSearchAccountInfoField.BILL_TYPE), "E")
            && StringUtil.equalsAnyIgnoreCase(CommonConstant.FLAG_FALSE,
            MapUtil.get(generalSearchAccountInfoResponseType.getResults(), GeneralSearchAccountInfoField.IS_SEN_ITINERARY));
    }

    private boolean hasRequestTotalAuth(QueryAuthResponseType queryAuthResponseType) {
        List<CenterAuthInfoType> centerAuthInfoTypeList = Optional.ofNullable(queryAuthResponseType)
                .map(QueryAuthResponseType::getCenterAuthData)
                .map(QueryCenterAuthDataType::getCenterAuthInfos)
                .orElse(Lists.newArrayList())
                .stream().filter(Objects::nonNull)
                .filter(o -> StringUtil.equalsIgnoreCase(o.getSystem(), BLUESPACE_ADMIN))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(centerAuthInfoTypeList)) {
            return false;
        }
        return centerAuthInfoTypeList.stream().anyMatch(this::isRequestTotalValid);
    }

    private boolean isRequestTotalValid(CenterAuthInfoType centerAuthInfoType) {
        if (StringUtil.equalsIgnoreCase(centerAuthInfoType.getType(), "ADMIN")) {
            return true;
        }
        if (!isValidDate(centerAuthInfoType)) {
            return false;
        }
        if (!StringUtil.equalsIgnoreCase(centerAuthInfoType.getDataType(), "CORP")) {
            return false;
        }
        FunctionContentType functionContentType = Optional.of(centerAuthInfoType)
                .map(CenterAuthInfoType::getFunctionContents)
                .orElse(Lists.newArrayList())
                .stream().filter(Objects::nonNull)
                .filter(o -> StringUtil.equalsIgnoreCase(o.getCode(), "requestTotal"))
                .findFirst().orElse(null);
        return functionContentType != null;
    }

    private boolean isValidDate(CenterAuthInfoType centerAuthInfoType) {
        Calendar beginCalendar = null;
        Calendar endDateCalendar = null;
        try {
            Date beginDate = DateUtil.parseDate(centerAuthInfoType.getBeginTime(), DateUtil.YYYY_MM_DD);
            Date endDate = DateUtil.parseDate(centerAuthInfoType.getExpireTime(), DateUtil.YYYY_MM_DD);
            beginCalendar = Calendar.getInstance();
            if (beginDate == null) {
                beginCalendar = null;
            } else {
                beginCalendar.setTime(beginDate);
            }
            endDateCalendar = Calendar.getInstance();
            if (endDate == null) {
                endDateCalendar = null;
            } else {
                endDateCalendar.setTime(endDate);
            }
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfNavHeaderInitResponseVO.class,
                    "isValidDate", ExceptionUtil.getException(e), null);
        }
        if (beginCalendar == null || endDateCalendar == null) {
            return true;
        }
        // 当前时间大于过期时间，不生效
        if (DateUtil.isTheFirstGreaterThanTheSecond(Calendar.getInstance(), endDateCalendar)) {
            return false;
        }
        // 当前时间小于生效时间，不生效
        if (DateUtil.isTheFirstLessThanTheSecond(Calendar.getInstance(), beginCalendar)) {
            return false;
        }
        return true;
    }

    private UserInfoVO buildUserInfoVO(GetCorpUserInfoResponseType getCorpUserInfoResponseType) {
        if (getCorpUserInfoResponseType == null) {
            return null;
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setPreferFirstName(getCorpUserInfoResponseType.getPreferredFirstName());
        userInfoVO.setPreferLastName(getCorpUserInfoResponseType.getPreferredLastName());
        return userInfoVO;
    }

    /**
     * 是否admin身份
     *
     * @param getCorpInfoResponseType
     * @param getCorpUserInfoResponseType
     * @return
     */
    private boolean isAdmin(GetCorpInfoResponseType getCorpInfoResponseType,
                            GetCorpUserInfoResponseType getCorpUserInfoResponseType,
                            CheckAuthResponseType checkAuthResponseType) {

        Short accountType = Optional.ofNullable(getCorpInfoResponseType).map(GetCorpInfoResponseType::getAccountType).orElse(null);
        String corpAdminPermission = Optional.ofNullable(getCorpUserInfoResponseType).map(GetCorpUserInfoResponseType::getCorpAdminPermission).orElse(null);
        boolean agentCorp = ACCOUNT_TYPE_AGENT.equals(accountType);
        boolean hasCorpAdminPermission = !StringUtil.isBlank(corpAdminPermission)
                && !StringUtil.equalsIgnoreCase("F", corpAdminPermission);
        if (agentCorp) {
            return hasCorpAdminPermission;
        }
        return checkAuthHasPermissionUnlimited(checkAuthResponseType) || checkAuthHasPermissionDiy(checkAuthResponseType);
    }

    /**
     * 开通所有权限
     *
     * @param checkAuthResponseType
     * @return
     */
    private boolean checkAuthHasPermissionUnlimited(CheckAuthResponseType checkAuthResponseType) {
        if (checkAuthResponseType == null || checkAuthResponseType.getCenterAuthData() == null) {
            return false;
        }
        CheckCenterAuthDataType checkCenterAuthDataType = checkAuthResponseType.getCenterAuthData();
        if (Boolean.TRUE.equals(checkCenterAuthDataType.isFunctionUnlimited())) {
            return true;
        }
        return false;
    }

    /**
     * 开通自定义权限
     *
     * @param checkAuthResponseType
     * @return
     */
    private boolean checkAuthHasPermissionDiy(CheckAuthResponseType checkAuthResponseType) {
        if (checkAuthResponseType == null || checkAuthResponseType.getCenterAuthData() == null) {
            return false;
        }
        CheckCenterAuthDataType checkCenterAuthDataType = checkAuthResponseType.getCenterAuthData();
        if (CollectionUtil.isEmpty(checkCenterAuthDataType.getFunctions())) {
            return false;
        }
        for (String checkAuthCode : NavHeaderInitConstant.CHECK_AUTH_CODES) {
            boolean currAuth = checkCenterAuthDataType.getFunctions()
                    .stream()
                    .filter(Objects::nonNull)
                    .anyMatch(function -> checkAuthCode.equalsIgnoreCase(function.getCode()));
            if (currAuth) {
                return true;
            }
        }
        return false;
    }

    private boolean checkCurrAuthHasPermission(String checkAuthCode, List<CenterAuthFunctionType> centerAuthFunctionTypes) {
        return centerAuthFunctionTypes
                .stream()
                .filter(Objects::nonNull)
                .anyMatch(function -> checkAuthCode.equalsIgnoreCase(function.getCode()));
    }


    private boolean isReporting(QueryAuthResponseType queryAuthResponseType) {
        if (queryAuthResponseType == null || queryAuthResponseType.getCenterAuthData() == null) {
            return false;
        }
        return Optional.ofNullable(queryAuthResponseType.getCenterAuthData())
                .map(QueryCenterAuthDataType::getCenterAuthInfos)
                .orElse(Lists.newArrayList())
                .stream().anyMatch(Objects::nonNull);
    }

    public MenuInfoVO buildMenuInfoVO(List<NavHeaderEnumInterface> navHeaderEnums) {
        if (CollectionUtil.isEmpty(navHeaderEnums)) {
            return null;
        }
        MenuInfoVO menuInfoVO = new MenuInfoVO();
        List<MenuUnitInfoVO> menuUnitInfoVOS = new ArrayList<>();
        menuInfoVO.setMenus(menuUnitInfoVOS);
        Set<GroupEnum> groupSet = new LinkedHashSet<>();
        // 菜单信息
        for (NavHeaderEnumInterface navHeaderEnum : navHeaderEnums) {
            MenuUnitInfoVO menuUnitInfoVO = new MenuUnitInfoVO();
            menuUnitInfoVO.setCode(navHeaderEnum.getCode());
            menuUnitInfoVO.setName(navHeaderEnum.getName());
            menuUnitInfoVO.setUrl(navHeaderEnum.getUrl(navHeaderUrlConfig));
            GroupEnum group = navHeaderEnum.getGroup();
            if (group != null) {
                menuUnitInfoVO.setGroupCode(group.getCode());
                groupSet.add(group);
            }
            menuUnitInfoVOS.add(menuUnitInfoVO);
        }
        // 分组信息
        if (CollectionUtil.isNotEmpty(groupSet)) {
            menuInfoVO.setGroups(new ArrayList<>(groupSet).stream().map(group -> {
                GroupInfoVO groupInfoVO = new GroupInfoVO();
                groupInfoVO.setGroupCode(group.getCode());
                groupInfoVO.setName(group.getName());
                return groupInfoVO;
            }).collect(Collectors.toList()));
        }
        return menuInfoVO;
    }


    /**
     * 出差申请
     *
     * @param customGrayResponseType
     * @param generalSearchAccountInfoResponseType
     * @return
     */
    private boolean supportEndorsement(CustomGrayResponseType customGrayResponseType,
                                       GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(CommonConstant.FLAG_TRUE, customGrayResponseType.getResults()
                .getOrDefault(NavHeaderInitConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, CommonConstant.FLAG_FALSE))) {
            return false;
        }
        if (generalSearchAccountInfoResponseType == null || MapUtils.isEmpty(generalSearchAccountInfoResponseType.getResults())) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(generalSearchAccountInfoResponseType.getResults()
                .get(GeneralSearchAccountInfoField.BILL_CONTROL_MODE), ENDORSEMENT_TYPE)) {
            return false;
        }
        return true;
    }

    private boolean supportApproval(CustomGrayResponseType customGrayResponseType,
                                    QueryApproveTaskResponseType queryApproveTaskResponseType) {
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return true;
        }
        if (!StringUtils.equalsIgnoreCase(CommonConstant.FLAG_TRUE, customGrayResponseType.getResults()
                .getOrDefault(APPROVAL_PERMISSION_NEW, CommonConstant.FLAG_FALSE))) {
            return true;
        }
        if (queryApproveTaskResponseType == null || BooleanUtils.isNotTrue(queryApproveTaskResponseType.isExistApproveTask())) {
            return false;
        }
        return true;
    }

    @Override
    protected ParamCheckResult check(Tuple1<NavHeaderInitResponse> tuple) {

        return null;
    }
}
