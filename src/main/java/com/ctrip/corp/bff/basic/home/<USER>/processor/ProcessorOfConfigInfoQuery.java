package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofconfiginfoquery.MapperOfConfigInfoQueryResponseVO;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 查询配置类信息，法务文档、隐私协议、语言列表等配置类信息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Component
public class ProcessorOfConfigInfoQuery extends AbstractProcessor<ConfigInfoQueryRequestVO, ConfigInfoQueryResponseVO> {

    @Autowired
    private MapperOfConfigInfoQueryResponseVO mapperOfConfigInfoQueryResponseVO;

    @Override
    public ConfigInfoQueryResponseVO execute(ConfigInfoQueryRequestVO request) {
        return mapperOfConfigInfoQueryResponseVO.map(Tuple1.of(request));
    }

    @Override
    public Map<String, String> tracking(ConfigInfoQueryRequestVO request, ConfigInfoQueryResponseVO response) {
        if (Objects.isNull(response) || Objects.isNull(response.getDocumentUrl()) || StringUtil.isAnyBlank(
            response.getDocumentUrl().getPrivacyStatementUrl(), response.getDocumentUrl().getTermsAndConditionUrl())) {
            Map<String, String> trackingMap = new HashMap<>();
            trackingMap.put("ProcessorOfConfigInfoQuery.response.documentUrl has null", "true");
            return trackingMap;
        }
        return null;
    }
}
