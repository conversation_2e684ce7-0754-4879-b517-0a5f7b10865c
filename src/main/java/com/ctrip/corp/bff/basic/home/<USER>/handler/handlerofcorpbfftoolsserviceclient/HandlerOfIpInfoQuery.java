package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient;


import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CorpBffToolsServiceClient;
import com.ctrip.corp.bff.tools.contract.IpInfoQueryRequestType;
import com.ctrip.corp.bff.tools.contract.IpInfoQueryResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Component
public class HandlerOfIpInfoQuery
    extends AbstractHandlerOfSOA<IpInfoQueryRequestType, IpInfoQueryResponseType, CorpBffToolsServiceClient> {
    @Override
    protected String getMethodName() {
        return "ipInfoQuery";
    }
}
