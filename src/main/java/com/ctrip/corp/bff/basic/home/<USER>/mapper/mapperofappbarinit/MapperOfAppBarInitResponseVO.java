package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappbarinit;

import com.ctrip.corp.bff.basic.contract.TabBarVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.AppBarConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.AppBarInfo;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.HostSiteConfig;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.APP_NEW_VERSION;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Component
public class MapperOfAppBarInitResponseVO extends
        AbstractMapper<Tuple4<TemplateSoaRequestType, CustomGrayResponseType, AppBarConfig, HostSiteConfig>, AppBarInitResponseVO> {

    private static final String DEFAULT_TAB_LIST = "defaultTabList";

    @Override
    protected AppBarInitResponseVO convert(Tuple4<TemplateSoaRequestType, CustomGrayResponseType, AppBarConfig, HostSiteConfig> tuple) {
        return buildAppBarInitResponseVO(tuple.getT1(), tuple.getT2(), tuple.getT3(), tuple.getT4());
    }

    @Override
    protected ParamCheckResult check(Tuple4<TemplateSoaRequestType, CustomGrayResponseType, AppBarConfig, HostSiteConfig> tuple) {
        return null;
    }

    private AppBarInitResponseVO buildAppBarInitResponseVO(TemplateSoaRequestType request,
                                                           CustomGrayResponseType customGrayResponseType,
                                                           AppBarConfig appBarConfig,
                                                           HostSiteConfig hostSiteConfig) {
        AppBarInitResponseVO response = new AppBarInitResponseVO();
        if (isNewVersion(customGrayResponseType)) {
            response.setNewVersion(BooleanConstant.TRUE);
            Map<String, List<AppBarInfo>> appBarMenu = appBarConfig.getNewVersionAppBar();
            response.setTabBars(buildTabBarList(appBarMenu, request, hostSiteConfig));
        } else {
            response.setNewVersion(BooleanConstant.FALSE);
            Map<String, List<AppBarInfo>> appBarMenu = appBarConfig.getOldVersionAppBar();
            response.setTabBars(buildTabBarList(appBarMenu, request, hostSiteConfig));
        }
        return response;
    }

    private List<TabBarVO> buildTabBarList(Map<String, List<AppBarInfo>> appBarMenu,
                                           TemplateSoaRequestType request, HostSiteConfig hostSiteConfig) {
        String site = getSite(request, hostSiteConfig);
        List<AppBarInfo> appBarTabList;
        if (StringUtils.isNotBlank(site) && MapUtils.getObject(appBarMenu, site) != null) {
            appBarTabList = MapUtils.getObject(appBarMenu, site, Lists.newArrayList());
        } else {
            appBarTabList = MapUtils.getObject(appBarMenu, DEFAULT_TAB_LIST, Lists.newArrayList());
        }
        return appBarTabList.stream().filter(Objects::nonNull).map(this::buildTabBarVO).collect(Collectors.toList());
    }

    private TabBarVO buildTabBarVO(AppBarInfo appBarInfo) {
        TabBarVO tabBarVO = new TabBarVO();
        tabBarVO.setCode(appBarInfo.getCode());
        String name = BFFSharkUtil.getSharkValue(appBarInfo.getSharkKey());
        if (StringUtils.isNotBlank(name)) {
            tabBarVO.setName(name);
        } else {
            tabBarVO.setName(appBarInfo.getName());
        }
        tabBarVO.setIconUrl(appBarInfo.getIconUrl());
        tabBarVO.setSelectedIconUrl(appBarInfo.getSelectedIconUrl());
        tabBarVO.setRedirectUrl(appBarInfo.getRedirectUrl());
        tabBarVO.setNameColor(appBarInfo.getNameColor());
        tabBarVO.setSelectedNameColor(appBarInfo.getSelectedNameColor());
        return tabBarVO;
    }

    private String getSite(TemplateSoaRequestType request, HostSiteConfig hostSiteConfig) {
        String gatewayHost = Optional.ofNullable(request)
                .map(TemplateSoaRequestType::getGatewayHost)
                .orElse(StringUtils.EMPTY);
        Map<String, String> hostSite = hostSiteConfig.getHostSite();
        if (MapUtils.isEmpty(hostSite) || StringUtils.isBlank(gatewayHost)) {
            return StringUtils.EMPTY;
        }
        return hostSite.getOrDefault(gatewayHost, StringUtils.EMPTY);
    }

    private boolean isNewVersion(CustomGrayResponseType customGrayResponseType) {
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return false;
        }
        Map<String, String> results = customGrayResponseType.getResults();
        return StringUtils.equalsIgnoreCase(results.getOrDefault(APP_NEW_VERSION, BooleanConstant.FALSE), BooleanConstant.TRUE);
    }

}
