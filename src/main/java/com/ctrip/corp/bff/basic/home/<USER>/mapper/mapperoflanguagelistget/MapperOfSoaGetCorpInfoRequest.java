package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget;

import com.ctrip.corp.bff.basic.home.trip.common.constant.GetCorpInfoRequestField;
import com.ctrip.corp.bff.basic.home.trip.qconfig.languagelist.LanguageConfig;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;

/**
 * @Description
 * @author:  mmt
 * @Date: 2024/6/3
 */
@Component
public class MapperOfSoaGetCorpInfoRequest extends AbstractMapper<Tuple1<String>, GetCorpInfoRequestType> {

    /**
     * 支持的语言配置
     */
    @QConfig("100045131#languageConfig.json")
    private LanguageConfig languageConfig;


    @Override
    protected GetCorpInfoRequestType convert(Tuple1<String> tuple1) {
        String corpId = tuple1.getT1();
        if (StringUtil.isBlank(corpId)) {
            return null;
        }
        GetCorpInfoRequestType corpInfoRequest = new GetCorpInfoRequestType();
        corpInfoRequest.setCorpId(corpId);
        corpInfoRequest.setSearchFields(Arrays.asList(GetCorpInfoRequestField.CORP_CUSTOMER_BRAND,
                GetCorpInfoRequestField.CORP_OPERATING_MODE));
        return corpInfoRequest;
    }

    @Override
    protected ParamCheckResult check(Tuple1<String> tuple) {
        return null;
    }



}
