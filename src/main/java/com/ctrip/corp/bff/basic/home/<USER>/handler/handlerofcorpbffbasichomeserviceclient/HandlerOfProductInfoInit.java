package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient;

import com.ctrip.corp.bff.basic.home.contract.*;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/19 16:14
 * @Version 1.0
 */
@Component
public class HandlerOfProductInfoInit
        extends AbstractHandlerOfSOA<ProductInfoInitRequestType, ProductInfoInitResponseType, CorpBffBasicHomeServiceClient> {
    @Override
    protected String getMethodName() {
        return "productInfoInit";
    }
}
