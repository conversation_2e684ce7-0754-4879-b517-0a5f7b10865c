package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflegaldocumentquery;

import com.ctrip.corp.approve.ws.contract.IFlightOrderInfo;
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant;
import com.ctrip.corp.bff.basic.home.trip.common.constant.GetCorpInfoRequestField;
import com.ctrip.corp.bff.basic.home.trip.common.enums.DocumentProductEnum;
import com.ctrip.corp.bff.basic.home.trip.contract.ContactUsInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList;
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.ContactUsInfoByCountryCodeConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigItem;
import com.ctrip.corp.bff.framework.template.common.defalut.LogDefaultUtil;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.google.common.collect.Lists;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description
 * @author: mmt
 * @Date: 2024/6/3
 */
@Component
public class MapperOfLegalDocumentResponse extends AbstractMapper<Tuple4<LegalDocumentQueryRequestVO,
        List<LegalDocumentConfigItem>, GetCorpInfoResponseType, String>, LegalDocumentQueryResponseVO> {

    @Autowired
    private MapperOfLanguageList mapperOfLanguageList;
    @QConfig("contactUsInfoByCountryCode.json")
    private List<ContactUsInfoByCountryCodeConfig> list;

    @Override
    protected LegalDocumentQueryResponseVO convert(Tuple4<LegalDocumentQueryRequestVO, List<LegalDocumentConfigItem>, GetCorpInfoResponseType, String> tuple2) {
        LegalDocumentQueryRequestVO request = tuple2.getT1();
        List<LegalDocumentConfigItem> legalDocumentConfigItemList = tuple2.getT2();
        GetCorpInfoResponseType getCorpInfoResponseType = tuple2.getT3();

        List<LegalDocumentConfigItem> configItemList = filterLegalDocumentConfigItem(request, legalDocumentConfigItemList, getCorpInfoResponseType, tuple2.getT4());

        LegalDocumentQueryResponseVO response = new LegalDocumentQueryResponseVO();
        // 隐私条款
        LegalDocumentConfigItem privacyStatementConfig =
                getConfigByEnum(configItemList, DocumentProductEnum.PRIVACY_STATEMENT);
        // 法务条款
        LegalDocumentConfigItem termsAndConditionsConfig =
                getConfigByEnum(configItemList, DocumentProductEnum.TERMS_AND_CONDITIONS);
        String statementPdfUrl = "";
        String termsPdfUrl = "";
        SourceFrom sourceFrom = Optional.ofNullable(request).map(LegalDocumentQueryRequestVO::getRequestHeader).map(TemplateSoaRequestType::getSourceFrom).orElse(null);
        if (SourceFrom.Native.equals(sourceFrom) || SourceFrom.H5.equals(sourceFrom)) {
            statementPdfUrl = Optional.ofNullable(privacyStatementConfig)
                    .map(LegalDocumentConfigItem::getH5PdfUrl)
                    .orElseGet(() -> LogDefaultUtil.bizDefaultVal(StringUtil.EMPTY,
                            "LegalDocumentQueryService.legalDocumentQuery", "privacyStatementConfigError"));
            // 内容pdfUrl: https://file.tripcdn.com/files/6/corp/0J82z12000apixkqz826D.pdf
            termsPdfUrl = Optional.ofNullable(termsAndConditionsConfig)
                    .map(LegalDocumentConfigItem::getH5PdfUrl)
                    .orElseGet(() -> LogDefaultUtil.bizDefaultVal(StringUtil.EMPTY,
                            "LegalDocumentQueryService.legalDocumentQuery", "termsAndConditionsConfigError"));
        }

        if (StringUtil.isBlank(statementPdfUrl)) {
            statementPdfUrl = Optional.ofNullable(privacyStatementConfig)
                    .map(LegalDocumentConfigItem::getPdfUrl)
                    .orElseGet(() -> LogDefaultUtil.bizDefaultVal(StringUtil.EMPTY,
                            "LegalDocumentQueryService.legalDocumentQuery", "privacyStatementConfigError"));
        }

        if (StringUtil.isBlank(termsPdfUrl)) {
            // 内容pdfUrl: https://file.tripcdn.com/files/6/corp/0J82z12000apixkqz826D.pdf
            termsPdfUrl = Optional.ofNullable(termsAndConditionsConfig)
                    .map(LegalDocumentConfigItem::getPdfUrl)
                    .orElseGet(() -> LogDefaultUtil.bizDefaultVal(StringUtil.EMPTY,
                            "LegalDocumentQueryService.legalDocumentQuery", "termsAndConditionsConfigError"));
        }
        response.setPrivacyStatementPdfUrl(statementPdfUrl);
        response.setTermsAndConditionsPdfUrl(termsPdfUrl);
        response.setContactUsInfo(getContactUsEmail(tuple2.getT4()));
        return response;


    }

    private ContactUsInfoVO getContactUsEmail(String t4) {
        if (StringUtil.isBlank(t4) || CollectionUtil.isEmpty(list)) {
            return null;
        }
        ContactUsInfoByCountryCodeConfig contactUsInfoConfig = list.stream()
                .filter(o -> StringUtil.equalsIgnoreCase(o.getCountryCode(), t4))
                .findFirst()
                .orElseGet(() ->
                        list.stream()
                                .filter(o -> StringUtil.equalsIgnoreCase(o.getCountryCode(), "default"))
                                .findFirst()
                                .orElse(new ContactUsInfoByCountryCodeConfig())
                );
        ContactUsInfoVO contactUsInfoVO = new ContactUsInfoVO();
        contactUsInfoVO.setContactUsEmail(contactUsInfoConfig.getEmail());
        contactUsInfoVO.setContactUsTel(contactUsInfoConfig.getTel());

        return contactUsInfoVO;

    }

    @Override
    protected ParamCheckResult check(Tuple4<LegalDocumentQueryRequestVO, List<LegalDocumentConfigItem>, GetCorpInfoResponseType, String> tuple) {
        return null;
    }


    private List<LegalDocumentConfigItem> filterLegalDocumentConfigItem(LegalDocumentQueryRequestVO request,
                                                                        List<LegalDocumentConfigItem> legalDocumentConfigItemList,
                                                                        GetCorpInfoResponseType getCorpInfoResponseType, String countryCode) {

        // 请求host
        String host = Optional.ofNullable(request).map(LegalDocumentQueryRequestVO::getRequestHeader)
            .map(TemplateSoaRequestType::getGatewayHost).orElse(null);
        // 判断是desk.trip域名就返回新文档
        if (HostUtil.isTripDeskHost(host)) {
            List<LegalDocumentConfigItem> items = getItemListByHost(legalDocumentConfigItemList, host);
            return items;
        }
        // 公司id
        String corpId = Optional.ofNullable(request)
                .map(LegalDocumentQueryRequestVO::getRequestHeader)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getCorpId)
                .orElse(null);
        String notLogin = CommonConstant.FLAG_TRUE;
        if (StringUtil.isNotBlank(corpId)) {
            notLogin = CommonConstant.FLAG_FALSE;
        }

        // 公司id
        Predicate<LegalDocumentConfigItem> configItemPredicateCorpId =
                o -> CollectionUtils.isEmpty(o.getCorpIdList())
                        || (StringUtil.isNotBlank(corpId) && CollectionUtil.containsIgnoreCase(o.getCorpIdList(), corpId));

        String language = Optional.ofNullable(request).map(LegalDocumentQueryRequestVO::getRequestHeader).map(TemplateSoaRequestType::getLanguage).orElse(null);
        // 语言
        Predicate<LegalDocumentConfigItem> configItemPredicateLanguage =
                o -> CollectionUtil.containsIgnoreCase(o.getLanguageList(), language);

        // 匹配配置信息集合
        List<LegalDocumentConfigItem> configItemListByPosOrBrand = legalDocumentConfigItemList.stream()
                // 公司id
                .filter(configItemPredicateCorpId)
                // 语言
                .filter(configItemPredicateLanguage)
                .collect(Collectors.toList());
        PosEnum posEnum = Optional.ofNullable(request).map(LegalDocumentQueryRequestVO::getRequestHeader).map(TemplateSoaRequestType::getPos).orElse(null);

        String pos = posEnum != null ? posEnum.getSiteCode() : null;

        String customerBrand = null;
        if (Objects.nonNull(getCorpInfoResponseType)) {
            customerBrand = mapperOfLanguageList.getCorpColSetValue(getCorpInfoResponseType, GetCorpInfoRequestField.CORP_CUSTOMER_BRAND);
        }

        return getconfigItemList(configItemListByPosOrBrand, customerBrand, pos, host, countryCode, notLogin);
    }

    // desk.trip域名返回新文档
    private List<LegalDocumentConfigItem> getItemListByHost(List<LegalDocumentConfigItem> configItemList, String host) {
        Predicate<LegalDocumentConfigItem> configItemPredicateHost =
            o -> o.getHostList().stream().anyMatch(host::contains);
        return configItemList.stream().filter(configItemPredicateHost).collect(Collectors.toList());
    }

    private List<LegalDocumentConfigItem> getconfigItemList(List<LegalDocumentConfigItem> configItemList,
                                                            String customerBrand,
                                                            String pos,
                                                            String host,
                                                            String countryCode,
                                                            String notLogin) {
        if (StringUtil.equalsIgnoreCase(notLogin, "T")) {
            // 优先用countryCode判断
            Predicate<LegalDocumentConfigItem> configItemPredicateCountryCode =
                    o -> (StringUtil.isNotBlank(countryCode)) && CollectionUtil.containsIgnoreCase(o.getCountryCodeList(), countryCode);
            // 匹配配置信息集合
            List<LegalDocumentConfigItem> configItemListByCountry = configItemList
                    .stream()
                    .filter(configItemPredicateCountryCode)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(configItemListByCountry)) {
                return configItemListByCountry;
            }
            // countryCode没匹配到,则用host匹配
            List<LegalDocumentConfigItem> collect = getItemListByHost(configItemList, host);
            if (CollectionUtil.isNotEmpty(collect)) {
                return collect;
            }

            // POS兜底
            Predicate<LegalDocumentConfigItem> configItemPredicatePos =
                    o -> StringUtil.equalsIgnoreCase(o.getPos(), pos);
            return configItemList
                    .stream()
                    .filter(configItemPredicatePos)
                    .collect(Collectors.toList());

        }

        // 登录后,优先用品牌名匹配
        Predicate<LegalDocumentConfigItem> configItemPredicateBrand =
                o -> (StringUtil.isNotBlank(customerBrand) && StringUtil.equalsIgnoreCase(o.getCustomerBrand(), customerBrand));

        // 匹配配置信息集合
        List<LegalDocumentConfigItem> configItemListByBrand = configItemList
                .stream()
                .filter(configItemPredicateBrand)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(configItemListByBrand)) {
            return configItemListByBrand;
        }

        // 品牌名没匹配到,则用pos匹配
        Predicate<LegalDocumentConfigItem> configItemPredicatePos =
                o -> StringUtil.equalsIgnoreCase(o.getPos(), pos);


        return configItemList
                .stream()
                .filter(configItemPredicatePos)
                .collect(Collectors.toList());

    }


    private LegalDocumentConfigItem getConfigByEnum(List<LegalDocumentConfigItem> configItemList,
                                                    DocumentProductEnum privacyStatement) {
        return Optional.ofNullable(configItemList)
                .orElseGet(() -> LogDefaultUtil.bizDefaultVal(Lists.newArrayList(),
                        "LegalDocumentQueryService.getConfigByEnum", "configItemListError"))
                .stream().filter(o -> privacyStatement.getCode().equalsIgnoreCase(o.getProduct()))
                .findFirst().orElseGet(() -> LogDefaultUtil.bizDefaultVal(null,
                        "LegalDocumentQueryService.getConfigByEnum", "configItemError"));
    }


}
