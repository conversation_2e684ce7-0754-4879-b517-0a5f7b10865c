package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.LanguageType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Description 公司用户信息接口请求
 * @Date: 2024/11/20 21:59
 * @Version 1.0
 */
@Component
public class MapperOfGetCorpUserInfoRequest extends AbstractMapper<Tuple1<TemplateSoaRequestType>, GetCorpUserInfoRequestType> {


    @Override
    protected GetCorpUserInfoRequestType convert(Tuple1<TemplateSoaRequestType> tuple) {
        TemplateSoaRequestType templateSoaRequestType = tuple.getT1();
        GetCorpUserInfoRequestType getCorpUserInfoRequestType = new GetCorpUserInfoRequestType();
        getCorpUserInfoRequestType.setUid(templateSoaRequestType.getHeader().getUserId());
        getCorpUserInfoRequestType.setLanguage(LanguageType.zh_cn);
        return getCorpUserInfoRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple) {
        return null;
    }
}
