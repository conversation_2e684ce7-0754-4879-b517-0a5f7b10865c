package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit;

import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@Component
public class MapperOfBrandInfoInitRequest extends AbstractMapper<Tuple2<TemplateSoaRequestType, String>, BrandInfoInitRequestType> {
    @Override
    protected BrandInfoInitRequestType convert(Tuple2<TemplateSoaRequestType, String> tuple) {
        BrandInfoInitRequestType requestType = new BrandInfoInitRequestType();
        TemplateSoaRequestType templateSoaRequestType = tuple.getT1();
        String scene = tuple.getT2();
        requestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(templateSoaRequestType));
        List<StrategyInfo> strategyInfos = Lists.newArrayList();
        requestType.setStrategyInfos(strategyInfos);
        if (StringUtils.isNotBlank(scene)) {
            StrategyInfo strategyInfo = new StrategyInfo();
            strategyInfo.setStrategyKey("scene");
            strategyInfo.setStrategyValue(scene);
            strategyInfos.add(strategyInfo);
        }
        String sourceFrom = Optional.ofNullable(templateSoaRequestType.getSourceFrom()).map(Enum::name).orElse(null);
        if (StringUtil.isNotBlank(sourceFrom)) {
            StrategyInfo strategyInfo = new StrategyInfo();
            strategyInfo.setStrategyKey("sourceFrom");
            strategyInfo.setStrategyValue(sourceFrom);
            strategyInfos.add(strategyInfo);
        }
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, String> tuple) {
        return null;
    }
}
