package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Description 导航头plan trip菜单信息
 * @Date: 2024/11/20 15:32
 * @Version 1.0
 */
public enum PlanTripEnum implements NavHeaderEnumInterface {

    /**
     * 机票
     */
    BOOK_FLIGHT("BOOK_FLIGHT", null, null, "bookFlightUrl", "FLIGHT"),

    /**
     * 酒店
     */
    BOOK_HOTEL("BOOK_HOTEL", null, null, "bookHotelUrl", "HOTEL"),

    /**
     * 火车
     */
    BOOK_TRAIN("BOOK_TRAIN", null, null, "bookTrainUrl", "TRAIN"),

    /**
     * 接送机
     */
    BOOK_AIRPORT_TRANSFER("BOOK_AIRPORT_TRANSFER", null, null, "bookAirportTransferUrl", "INTL_CAR_CH"),
    /**
     * 租车
     */
    BOOK_CAR_RENTAL("BOOK_CAR_RENTAL", null, null, "bookCarRentalUrl", "INTL_CAR_RENTAL"),
    ;


    PlanTripEnum(String code, String icon, GroupEnum group, String url, String productLine) {
        this.code = code;
        this.icon = icon;
        this.group = group;
        this.url = url;
        this.productLine = productLine;
    }

    /**
     * code
     */
    private final String code;

    /**
     * 图标
     */
    private final String icon;

    /**
     * 分组code
     */
    private final GroupEnum group;

    /**
     * 链接
     */
    private final String url;

    /**
     * 产线
     */
    private final String productLine;


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public GroupEnum getGroup() {
        return group;
    }

    @Override
    public String getName() {
        return BFFSharkUtil.getSharkValue("trip.biz.common.biz.text.header.plan_trip." + name().toLowerCase());
    }

    @Override
    public String getUrl(Map<String, String> navHeaderUrlConfig) {
        if (MapUtil.isEmpty(navHeaderUrlConfig)) {
            return "";
        }
        return navHeaderUrlConfig.get(url);
    }

    public static PlanTripEnum getByProductLine(String productLine) {
        if (StringUtil.isBlank(productLine)) {
            return null;
        }
        for (PlanTripEnum value : PlanTripEnum.values()) {
            if (StringUtil.equalsIgnoreCase(value.productLine, productLine)) {
                return value;
            }
        }
        return null;
    }
}
