package com.ctrip.corp.bff.basic.home.trip.service;

import org.springframework.beans.factory.annotation.Autowired;

import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfAnnouncementQuery;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@WebService(name = "announcementQuery")
public class ServiceOfAnnouncementQuery
    extends AbstractSyncService<AnnouncementQueryRequestVO, AnnouncementQueryResponseVO> {

    @Autowired
    private ProcessorOfAnnouncementQuery processorOfAnnouncementQuery;

    @Override
    public void validateRequest(AnnouncementQueryRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<AnnouncementQueryRequestVO, AnnouncementQueryResponseVO>
        getProcessor(AnnouncementQueryRequestVO request) {
        return processorOfAnnouncementQuery;
    }
}
