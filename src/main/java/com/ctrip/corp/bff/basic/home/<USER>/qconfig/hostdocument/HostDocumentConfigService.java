package com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument;

import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.spring.QConfig;

/**
 * <AUTHOR>
 * @Date 2025/7/22 20:47
 */
@Service
public class HostDocumentConfigService {

    @QConfig("HostDocumentConfig.json")
    private HostDocumentConfig hostDocumentConfig;

    public void setHostDocumentConfig(HostDocumentConfig hostDocumentConfig) {
        this.hostDocumentConfig = hostDocumentConfig;
    }

    public HostDocumentConfig getHostDocumentConfig() {
        return hostDocumentConfig;
    }
}
