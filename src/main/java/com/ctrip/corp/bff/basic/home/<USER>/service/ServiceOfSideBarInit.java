package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorSideBarInit;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@WebService(name = "sideBarInit")
public class ServiceOfSideBarInit extends AbstractSyncService<SideBarInitRequestVO, SideBarInitResponseVO> {

    @Autowired
    private ProcessorSideBarInit processor;

    @Override
    public void validateRequest(SideBarInitRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<SideBarInitRequestVO, SideBarInitResponseVO> getProcessor(SideBarInitRequestVO request) {
        return processor;
    }
}
