package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfLanguageListQuery;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@WebService(name = "languageListQuery")
public class ServiceOfLanguageListQuery extends AbstractSyncService<LanguageListQueryRequestVO, LanguageListQueryResponseVO> {

    @Autowired
    private ProcessorOfLanguageListQuery processor;

    @Override
    public void validateRequest(LanguageListQueryRequestVO requestType) throws BusinessException {
    }

    @Override
    protected Processor<LanguageListQueryRequestVO, LanguageListQueryResponseVO> getProcessor(LanguageListQueryRequestVO requestType) {
        return processor;
    }
}
