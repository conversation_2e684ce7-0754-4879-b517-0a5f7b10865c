package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfAppBarInit;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@WebService(name = "appBarInit")
public class ServiceOfAppBarInit extends AbstractSyncService<AppBarInitRequestVO, AppBarInitResponseVO> {

    @Autowired
    private ProcessorOfAppBarInit processor;

    @Override
    public void validateRequest(AppBarInitRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<AppBarInitRequestVO, AppBarInitResponseVO> getProcessor(AppBarInitRequestVO request) {
        return processor;
    }
}
