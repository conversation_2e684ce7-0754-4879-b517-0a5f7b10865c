package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corp4jservice.Corp4jServiceClient;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * @Description
 * @author:  mmt
 * @Date: 2024/6/3
 */  
@Component
public class HandlerOfGetCorpInfo
    extends AbstractHandlerOfSOA<GetCorpInfoRequestType, GetCorpInfoResponseType, Corp4jServiceClient> {
    @Override
    protected String getMethodName() {
        return "getCorpInfo";
    }
}
