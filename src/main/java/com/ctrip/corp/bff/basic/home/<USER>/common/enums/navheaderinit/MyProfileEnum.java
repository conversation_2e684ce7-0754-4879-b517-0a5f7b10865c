package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/9 15:15
 * @Version 1.0
 */
public enum MyProfileEnum implements NavHeaderEnumInterface {
    /**
     * 出差申请
     */
    ENDORSEMENT("ENDORSEMENT"),

    /**
     * 个人信息
     */
    PERSONAL_INFO("PERSONAL_INFO"),

    /**
     * 证件
     */
    TRAVEL_DOCUMENT("TRAVEL_DOCUMENT"),

    /**
     * 旅客信息
     */
    GUEST_TRAVELER_INFO("GUEST_TRAVELER_INFO"),

    /**
     * 设置
     */
    SETTING("SETTING"),

    /**
     * 安全
     */
    PAYMENT_CARD("PAYMENT_CARD"),

    /**
     * 会员
     */
    LOYALTY_MEMBERSHIP("LOYALTY_MEMBERSHIP"),

    /**
     * 优惠券
     */
    PROMO_CODE("PROMO_CODE"),

    /**
     * 代理审批
     */
    DELEGATE_APPROVAL("DELEGATE_APPROVAL"),

    /**
     * 会话
     */
    INBOX("INBOX"),

    /**
     * 钱包（旅贝）
     */
    WALLET("WALLET"),

    /**
     * 登出
     */
    LOGOUT("LOGOUT")


    ;

    MyProfileEnum(String code) {
        this.code = code;
    }

    /**
     * code
     */
    private final String code;


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public GroupEnum getGroup() {
        return null;
    }

    @Override
    public String getName() {
        return BFFSharkUtil.getSharkValue("trip.biz.bff.basic.biz.my_profile." + name().toLowerCase());
    }

    @Override
    public String getUrl(Map<String, String> navHeaderUrlConfig) {
        if (MapUtil.isEmpty(navHeaderUrlConfig)) {
            return "";
        }
        return navHeaderUrlConfig.get(StringUtil.toCamelCase(code) + "Url");
    }

    public static MyProfileEnum getByMyProfileCode(String code) {
        if (StringUtil.isBlank(code)) {
            return null;
        }
        for (MyProfileEnum value : MyProfileEnum.values()) {
            if (StringUtil.equalsIgnoreCase(value.code, code)) {
                return value;
            }
        }
        return null;
    }
}
