package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient;

import com.ctrip.corp.bff.basic.home.contract.CorpBffBasicHomeServiceClient;
import com.ctrip.corp.bff.basic.home.contract.SideBarInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.SideBarInitResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
public class HandlerOfSideBarInit extends AbstractHandlerOfSOA<SideBarInitRequestType, SideBarInitResponseType, CorpBffBasicHomeServiceClient> {
    @Override
    protected String getMethodName() {
        return "sideBarInit";
    }
}
