package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;

/**
 * @Author: z.c<PERSON> wang
 * @Description 菜单分组信息
 * @Date: 2024/11/20 17:00
 * @Version 1.0
 */
public enum GroupEnum {
    /**
     * REPORTING
     */
    REPORTING("REPORTING"),

    /**
     * SETTING
     */
    SETTING("SETTING"),

    /**
     * MANAGEMENT
     */
    MANAGEMENT("MANAGEMENT")
    ;

    /**
     * code
     */
    private final String code;

    GroupEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return BFFSharkUtil.getSharkValue("trip.biz.common.biz.text.header.group." + name().toLowerCase());
    }
}
