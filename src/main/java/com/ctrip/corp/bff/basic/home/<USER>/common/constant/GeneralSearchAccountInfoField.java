package com.ctrip.corp.bff.basic.home.trip.common.constant;

/**
 * @Description 主账户接口请求字段名称
 * @author:  mmt
 * @Date: 2023/6/13
 */
public class GeneralSearchAccountInfoField {
    /**
     * 对国内机票产品的控制开关PLCS->ProductLineControlSwitch
     */
    public static final String PLCS_FLIGHT_N = "PLCSFlightN";

    /**
     * 对国际机票产品的控制开关PLCS->ProductLineControlSwitch
     */
    public static final String PLCS_FLIGHT_I = "PLCSFlightI";
    /**
     * 对酒店产品的控制开关 PLCS->ProductLineControlSwitch
     */
    public static final String PLCS_HOTLE = "PLCSHotle";
    /**
     * 对火车票产品的控制开关 PLCS->ProductLineControlSwitch
     */
    public static final String PLCS_TRAIN = "PLCSTrain";
    /**
     * 对海外火车票产品的控制开关 PLCS->ProductLineControlSwitch
     */
    public static final String PLCS_TRAIN_I = "PLCSTrain_I";
    /**
     * 对国际打车产品的控制开关 PLCS->PLCSCallCarCharteredInt
     */
    public static final String PLCS_ONCALL = "PLCSCallCarCharteredInt";
    /**
     * 对国际接送机产品的控制开关 PLCS->PLCSCarAirportIntl
     */
    public static final String PLCS_CAR_AIRPORT = "PLCSCarAirportIntl";
    /**
     * online、app可选语言屏蔽 B:繁中
     */
    public static final String SHIELD_ONLINE_APP_LANGUAGE = "ShieldOnlineAppLanguage";
    /**
     * 子账户信息 -单据管控
     */
    public static final String BILL_CONTROL_MODE = "BillControlMode";

    /**
     * 账户配置查询字段（单订单\行程打包单） E:行程打包单
     */
    public static String BILL_TYPE = "BillType";

    /**
     * 账户配置查询字段（有感\无感）
     */
    public static String IS_SEN_ITINERARY = "IsSenItinerary";

}
