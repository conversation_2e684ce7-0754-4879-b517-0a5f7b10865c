package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.contract.SideBarInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.SideBarInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.SideBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfSideBarInit;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit.MapperOfSideBarInitRequestType;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofsidebarinit.MapperOfSideBarInitResponseVO;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
public class ProcessorSideBarInit extends AbstractProcessor<SideBarInitRequestVO, SideBarInitResponseVO> {

    @Autowired
    private MapperOfSideBarInitResponseVO mapperOfSideBarInitResponseVO;
    @Autowired
    private MapperOfSideBarInitRequestType mapperOfSideBarInitRequestType;
    @Autowired
    private HandlerOfSideBarInit handlerOfSideBarInit;

    @Override
    public SideBarInitResponseVO execute(SideBarInitRequestVO request) throws Exception {
        SideBarInitRequestType sideBarInitRequestType = mapperOfSideBarInitRequestType.map(Tuple1.of(request));
        return mapperOfSideBarInitResponseVO.map(Tuple1.of(handlerOfSideBarInit.handleAsync(sideBarInitRequestType).get()));
    }

    @Override
    public Map<String, String> tracking(SideBarInitRequestVO request, SideBarInitResponseVO response) {
        return null;
    }
}
