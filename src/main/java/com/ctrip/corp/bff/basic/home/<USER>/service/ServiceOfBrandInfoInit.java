package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfBrandInfoInit;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@WebService(name = "brandInfoInit")
public class ServiceOfBrandInfoInit extends AbstractSyncService<BrandInfoInitRequestVO, BrandInfoInitResponseVO> {

    @Autowired
    private ProcessorOfBrandInfoInit processor;

    @Override
    public void validateRequest(BrandInfoInitRequestVO requestType) throws BusinessException {
    }

    @Override
    protected Processor<BrandInfoInitRequestVO, BrandInfoInitResponseVO> getProcessor(BrandInfoInitRequestVO requestType) {
        return processor;
    }
}
