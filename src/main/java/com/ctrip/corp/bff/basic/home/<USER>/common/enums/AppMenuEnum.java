package com.ctrip.corp.bff.basic.home.trip.common.enums;

/**
 * <AUTHOR>
 * @date 2025/2/28
 */
public enum AppMenuEnum {

    /**
     * bookTrip
     */
    BOOK_TRIP("bookTrip"),
    /**
     * manageTrip
     */
    MANAGE_TRIP("manageTrip"),
    /**
     * approveTrip
     */
    APPROVE_TRIP("approveTrip"),
    /**
     * travelRequest
     */
    TRAVEL_REQUEST("travelRequest"),
    /**
     * myProfile
     */
    MY_PROFILE("myProfile"),
    /**
     * upcomingTrip
     */
    UPCOMING_TRIP("upcomingTrip"),
    /**
     * allBooking
     */
    ALL_BOOKING("allBooking"),
    /**
     * journey
     */
    JOURNEY("journey"),
    /**
     * itinerary
     */
    ITINERARY("itinerary"),
    ;

    private final String code;

    AppMenuEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

}
