package com.ctrip.corp.bff.basic.home.trip.qconfig.languagelist;

import java.util.List;
import java.util.Map;

/**
 * @Description    
 * @author:  mmt
 * @Date: 2024/8/5
 */  
public class LanguageConfig {


    Map<String, List<LanguageInfo>> hostConfigMap;

    Map<String, List<LanguageInfo>> posConfigMap;

    Map<String, List<LanguageInfo>> brandConfigMap;

    Map<String, List<LanguageInfo>> customSceneMap;
    public Map<String, List<LanguageInfo>> getHostConfigMap() {
        return hostConfigMap;
    }

    public void setHostConfigMap(Map<String, List<LanguageInfo>> hostConfigMap) {
        this.hostConfigMap = hostConfigMap;
    }

    public Map<String, List<LanguageInfo>> getPosConfigMap() {
        return posConfigMap;
    }

    public void setPosConfigMap(Map<String, List<LanguageInfo>> posConfigMap) {
        this.posConfigMap = posConfigMap;
    }

    public Map<String, List<LanguageInfo>> getBrandConfigMap() {
        return brandConfigMap;
    }

    public void setBrandConfigMap(Map<String, List<LanguageInfo>> brandConfigMap) {
        this.brandConfigMap = brandConfigMap;
    }
    public Map<String, List<LanguageInfo>> getCustomSceneMap() {
        return customSceneMap;
    }

    public void setCustomSceneMap(
        Map<String, List<LanguageInfo>> customSceneMap) {
        this.customSceneMap = customSceneMap;
    }


}
