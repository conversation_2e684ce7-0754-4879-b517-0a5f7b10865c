package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit;

import com.ctrip.corp.bff.basic.home.contract.AgreementInfo;
import com.ctrip.corp.bff.basic.home.contract.BrandInfo;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.contract.AgreementInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoVO;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitResponseVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author:  renyiwang
 * @Date: 2024/6/14
 */
@Component
public class MapperOfBrandInfoInitResponseVO extends AbstractMapper<Tuple1<BrandInfoInitResponseType>, BrandInfoInitResponseVO> {


    @Override
    protected BrandInfoInitResponseVO convert(Tuple1<BrandInfoInitResponseType> tuple1) {

        if (Objects.isNull(tuple1) || Objects.isNull(tuple1.getT1())) {
            return new BrandInfoInitResponseVO();
        }

        BrandInfoInitResponseType t1 = tuple1.getT1();
        BrandInfoInitResponseVO vo = new BrandInfoInitResponseVO();
        vo.setBrandInfo(buildBrandInfo(t1.getBrandInfo()));
        vo.setFooterLogo(t1.getFooterLogo());
        vo.setAgreementInfos(buildAgreementInfos(t1.getAgreementInfos()));
        return vo;

    }

    private List<AgreementInfoVO> buildAgreementInfos(List<AgreementInfo> agreementInfos) {
        if (CollectionUtil.isEmpty(agreementInfos)) {
            return null;
        }
        return  agreementInfos.stream().map(agreementInfo -> {
            AgreementInfoVO agreementInfoVO = new AgreementInfoVO();
            agreementInfoVO.setCode(agreementInfo.getCode());
            agreementInfoVO.setName(BFFSharkUtil.getSharkValue("trip.biz.common.biz.text.footer.agreement." + agreementInfo.getCode()));
            agreementInfoVO.setUrl(agreementInfo.getUrl());
            return agreementInfoVO;
        }).collect(Collectors.toList());
    }

    public static BrandInfoVO buildBrandInfo(BrandInfo brandInfo) {
        if (Objects.isNull(brandInfo)) {
            return null;
        }
        BrandInfoVO vo = new BrandInfoVO();
        vo.setCustomerBrand(brandInfo.getCustomerBrand());
        vo.setBrowserIcon(brandInfo.getBrowserIcon());
        vo.setCopyright(brandInfo.getCopyright());
        vo.setLogoMap(brandInfo.getLogoMap());
        vo.setJumpContactUsUrl(brandInfo.getJumpContactUsUrl());
        return vo;


    }

    @Override
    protected ParamCheckResult check(Tuple1<BrandInfoInitResponseType> brandInfoInitResponseTypeTuple1) {
        return null;
    }
}
