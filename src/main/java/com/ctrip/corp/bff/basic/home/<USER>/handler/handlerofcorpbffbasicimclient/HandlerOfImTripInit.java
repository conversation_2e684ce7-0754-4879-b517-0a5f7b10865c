package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/9 17:19
 * @Version 1.0
 */
@Component
public class HandlerOfImTripInit extends AbstractHandlerOfSOA<ImTripInitRequestType, ImTripInitResponseType, CorpBffBasicImServiceClient> {
    @Override
    protected String getMethodName() {
        return "imTripInit";
    }
}
