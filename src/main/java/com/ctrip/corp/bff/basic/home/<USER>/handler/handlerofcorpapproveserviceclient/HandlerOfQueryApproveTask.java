package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient;

import com.ctrip.corp.approve.ws.contract.CorpApproveServiceClient;
import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType;
import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class HandlerOfQueryApproveTask extends AbstractHandlerOfSOA<QueryApproveTaskRequestType, QueryApproveTaskResponseType, CorpApproveServiceClient> {
    @Override
    protected String getMethodName() {
        return "queryApproveTask";
    }
}