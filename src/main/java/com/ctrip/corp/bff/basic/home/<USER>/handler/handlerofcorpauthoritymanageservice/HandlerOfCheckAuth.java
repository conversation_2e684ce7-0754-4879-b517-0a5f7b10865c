package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.authorityManage.*;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/10 14:20
 * @Version 1.0
 */
@Component
public class HandlerOfCheckAuth extends
        AbstractHandlerOfSOA<CheckAuthRequestType, CheckAuthResponseType, CorpAuthorityManageServiceClient> {
    @Override
    protected String getMethodName() {
        return "checkAuth";
    }
}
