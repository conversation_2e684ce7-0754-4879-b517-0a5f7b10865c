package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * @Description
 * @author:  mmt
 * @Date: 2024/6/3
 */  
@Component
public class HandlerOfGeneralSearchAccountInfo
    extends AbstractHandlerOfSOA<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType, CorpAccountQueryServiceClient> {
    @Override
    protected String getMethodName() {
        return "generalSearchAccountInfo";
    }
}
