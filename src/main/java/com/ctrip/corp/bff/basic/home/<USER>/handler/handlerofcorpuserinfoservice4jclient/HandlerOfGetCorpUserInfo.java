package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/11/20 21:21
 * @Version 1.0
 */
@Component
public class HandlerOfGetCorpUserInfo
        extends AbstractHandlerOfSOA<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType, CorpUserInfoService4jClient> {
    @Override
    protected String getMethodName() {
        return "getCorpUserInfo";
    }
}
