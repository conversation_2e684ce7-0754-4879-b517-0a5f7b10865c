package com.ctrip.corp.bff.basic.home.trip.processor;

import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.basic.home.contract.AnnouncementQueryResponseType;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AnnouncementQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfAnnouncementQuery;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfAnnouncementQueryRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfAnnouncementQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfGetCityBaseInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery.MapperOfGetCityBaseInfoResponse;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;

/**
 * <AUTHOR>
 * @date 2024/5/16 21:40
 */
@Component
public class ProcessorOfAnnouncementQuery
    extends AbstractProcessor<AnnouncementQueryRequestVO, AnnouncementQueryResponseVO> {

    @Autowired
    private HandlerOfAnnouncementQuery handlerOfAnnouncementQuery;
    @Autowired
    private MapperOfAnnouncementQueryRequest mapperOfAnnouncementQueryRequest;
    @Autowired
    private MapperOfAnnouncementQueryResponseVO mapperOfAnnouncementQueryResponseVO;

    @Autowired
    private HandlerOfGetCityBaseInfo handlerOfGetCityBaseInfo;

    @Autowired
    private MapperOfGetCityBaseInfoRequest mapperOfGetCityBaseInfoRequest;

    @Autowired
    private MapperOfGetCityBaseInfoResponse mapperOfGetCityBaseInfoResponse;

    @Override
    public AnnouncementQueryResponseVO execute(AnnouncementQueryRequestVO request) {

        if (Objects.isNull(request)) {
            return new AnnouncementQueryResponseVO();
        }

        // 使用cityIds获取国家id
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = null;
        if (CollectionUtil.isNotEmpty(request.getCityIds())) {
            GetCityBaseInfoRequestType getCityBaseInfoRequestType = mapperOfGetCityBaseInfoRequest.map(Tuple2.of(request.getCityIds(), request.getRequestHeader()));
            if (Objects.nonNull(getCityBaseInfoRequestType)) {
                getCityBaseInfoResponseType = handlerOfGetCityBaseInfo.handleAsync(getCityBaseInfoRequestType).getWithoutError();
            }
        }
        AnnouncementQueryResponseType announcementQueryResponseType =
            handlerOfAnnouncementQuery.handleAsync(mapperOfAnnouncementQueryRequest.map(Tuple2.of(request, getCityBaseInfoResponseType))).getWithoutError();

        return mapperOfAnnouncementQueryResponseVO.map(Tuple1.of(announcementQueryResponseType));
    }

    @Override
    public Map<String, String> tracking(AnnouncementQueryRequestVO request, AnnouncementQueryResponseVO response) {
        return null;
    }
}
