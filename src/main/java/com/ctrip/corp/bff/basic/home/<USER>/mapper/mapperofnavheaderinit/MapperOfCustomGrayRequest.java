package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
@Component
public class MapperOfCustomGrayRequest
    extends AbstractMapper<Tuple2<TemplateSoaRequestType, List<String>>, CustomGrayRequestType> {
    @Override
    protected CustomGrayRequestType convert(Tuple2<TemplateSoaRequestType, List<String>> tuple) {
        CustomGrayRequestType request = new CustomGrayRequestType();
        request.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(tuple.getT1()));
        request.setKeys(tuple.getT2());
        return request;
    }

    @Override
    protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, List<String>> tuple) {
        return null;
    }
}
