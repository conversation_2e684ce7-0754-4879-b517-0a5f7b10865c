package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofconfiginfoquery;

import com.ctrip.corp.bff.basic.home.trip.common.enums.DocumentProductEnum;
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.contract.DocumentUrlVO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentBO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.HostDocumentConfigService;
import com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument.StatementBO;
import com.ctrip.corp.bff.framework.template.common.language.LanguageUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadContextUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description
 * @author: mmt
 * @Date: 2024/6/3
 */
@Component
public class MapperOfConfigInfoQueryResponseVO
    extends AbstractMapper<Tuple1<ConfigInfoQueryRequestVO>, ConfigInfoQueryResponseVO> {

    @Autowired
    private HostDocumentConfigService hostDocumentConfigService;

    @Override
    protected ConfigInfoQueryResponseVO convert(Tuple1<ConfigInfoQueryRequestVO> tuple) {

        ConfigInfoQueryResponseVO configInfoQueryResponse = new ConfigInfoQueryResponseVO();
        configInfoQueryResponse.setDocumentUrl(buildDocumentUrl());
        return configInfoQueryResponse;
    }

    protected DocumentUrlVO buildDocumentUrl() {
        HostDocumentConfig hostDocumentConfig = hostDocumentConfigService.getHostDocumentConfig();
        DocumentUrlVO documentUrl = new DocumentUrlVO();
        if (hostDocumentConfig == null) {
            return documentUrl;
        }
        // 1、按host匹配配置项
        List<HostDocumentBO> hostDocumentList = hostDocumentConfig.getHostDocumentList();
        HostDocumentBO hostDocumentBO = filterHostDocumentBO(hostDocumentList, ThreadContextUtil.getGatewayHost());
        if (hostDocumentBO == null) {
            // 2、对应的host没有配置项，按默认主站（www.trip.biz）匹配
            hostDocumentBO = filterHostDocumentBO(hostDocumentList, "www.trip.biz");
        }
        if (hostDocumentBO == null) {
            return documentUrl;
        }
        DocumentUrlVO documentUrlVO = new DocumentUrlVO();
        documentUrlVO.setPrivacyStatementUrl(filterDocument(hostDocumentBO, DocumentProductEnum.PRIVACY_STATEMENT));
        documentUrlVO.setTermsAndConditionUrl(filterDocument(hostDocumentBO, DocumentProductEnum.TERMS_AND_CONDITIONS));
        return documentUrlVO;
    }

    protected String filterDocument(HostDocumentBO hostDocument, DocumentProductEnum documentProductEnum) {
        if (hostDocument == null) {
            return null;
        }
        List<StatementBO> statementList = new ArrayList<>();
        if (documentProductEnum == DocumentProductEnum.PRIVACY_STATEMENT) {
            statementList = hostDocument.getPrivacyStatementList();
        }
        if (documentProductEnum == DocumentProductEnum.TERMS_AND_CONDITIONS) {
            statementList = hostDocument.getTermsAndConditionList();
        }
        StatementBO statementBO = filterStatementBO(statementList, LanguageUtil.getLocale().getLanguageLocaleString());
        if (statementBO == null) {
            // 如果没有找到对应语言的文档，则使用默认语言的文档
            statementBO = filterStatementBO(statementList, "defaultLanguage");
        }
        if (statementBO == null) {
            return null;
        }
        return statementBO.getDocumentUrl();
    }

    protected StatementBO filterStatementBO(List<StatementBO> statementList, String language) {
        if (statementList == null || statementList.isEmpty()) {
            return null;
        }
        for (StatementBO statementBO : statementList) {
            List<String> languageList = statementBO.getLanguageList();
            if (CollectionUtil.containsIgnoreCase(languageList, language)) {
                return statementBO;
            }
        }
        return null;

    }

    protected HostDocumentBO filterHostDocumentBO(List<HostDocumentBO> hostDocumentList, String host) {
        if (hostDocumentList == null || hostDocumentList.isEmpty()) {
            return null;
        }
        for (HostDocumentBO hostDocumentBO : hostDocumentList) {
            List<String> hostList = hostDocumentBO.getHostList();
            if (CollectionUtil.containsIgnoreCase(hostList, host)) {
                return hostDocumentBO;
            }
            boolean match = Optional.ofNullable(hostList).orElse(new ArrayList<>()).stream().anyMatch(host::matches);
            if (match) {
                return hostDocumentBO;
            }
        }
        return null;
    }

    @Override
    protected ParamCheckResult check(Tuple1<ConfigInfoQueryRequestVO> tuple) {
        return null;
    }
}
