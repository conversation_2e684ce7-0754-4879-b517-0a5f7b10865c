package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType;
import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.builder.NavHeaderInitResponse;
import com.ctrip.corp.bff.basic.home.trip.common.constant.NavHeaderInitConstant;
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfQueryApproveTaskRequestType;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient.HandlerOfQueryApproveTask;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice.HandlerOfCheckAuth;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice.HandlerOfQueryAuth;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfImTripInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffmcinfoserviceclient.HandlerOfMyProfileMenuInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpsettlementsettingsuncoresvcapplicationclient.HandlerOfCheckCustomerCbkPermission;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.*;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitRequestType;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionRequestType;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.authorityManage.CheckAuthRequestType;
import corp.user.service.authorityManage.CheckAuthResponseType;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.APPROVAL_PERMISSION_NEW;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.BLUESPACE_ADMIN;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.ONLINE_REPORT;

/**
 * @Author: z.c. wang
 * @Description 导航头
 * @Date: 2024/11/20 15:23
 * @Version 1.0
 */
@Component
public class ProcessorOfNavHeaderInit extends AbstractProcessor<NavHeaderInitRequestVO, NavHeaderInitResponseVO> {

    @Autowired
    private MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest;
    @Autowired
    private HandlerOfGetCorpInfo handlerOfGetCorpInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfNavHeaderInitResponseVO mapperOfNavHeaderInitResponseVO;
    @Autowired
    private MapperOfQueryAuthRequest mapperOfQueryAuthRequest;
    @Autowired
    private HandlerOfQueryAuth handlerOfQueryAuth;
    @Autowired
    private MapperOfCheckAuthRequest mapperOfCheckAuthRequest;
    @Autowired
    private HandlerOfCheckAuth handlerOfCheckAuth;
    @Autowired
    private MapperofSoaGeneralSearchAccountInfoRequest mapperofSoaGeneralSearchAccountInfoRequest;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private MapperOfCustomGrayRequest mapperOfCustomGrayRequest;
    @Autowired
    private HandlerOfCustomGray handlerOfCustomGray;
    @Autowired
    private MapperOfProductInfoInitRequest mapperOfProductInfoInitRequest;
    @Autowired
    private HandlerOfProductInfoInit handlerOfProductInfoInit;
    @Autowired
    private MapperOfCheckCustomerCbkPermissionRequest mapperOfCheckCustomerCbkPermissionRequest;
    @Autowired
    private HandlerOfCheckCustomerCbkPermission handlerOfCheckCustomerCbkPermission;
    @Autowired
    private HandlerOfBrandInfoInit handlerOfBrandInfoInit;
    @Autowired
    private MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest;
    @Autowired
    private MapperOfMyProfileMenuInitRequestType mapperOfMyProfileMenuInitRequestType;
    @Autowired
    private HandlerOfMyProfileMenuInit handlerOfMyProfileMenuInit;
    @Autowired
    private MapperOfImTripInitRequest mapperOfImTripInitRequest;
    @Autowired
    private HandlerOfImTripInit handlerOfImTripInit;

    @Autowired
    private HandlerOfQueryApproveTask handlerOfQueryApproveTask;
    @Autowired
    private MapperOfQueryApproveTaskRequestType mapperOfQueryApproveTaskRequestType;

    @Override
    public NavHeaderInitResponseVO execute(NavHeaderInitRequestVO request) throws Exception {
        boolean checkRequest = checkRequest(request);
        WaitFuture<GetCorpInfoRequestType, GetCorpInfoResponseType> getCorpInfoWaitFuture = null;
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoWaitFuture = null;
        WaitFuture<QueryAuthRequestType, QueryAuthResponseType> queryAuthReportWaitFuture = null;
        WaitFuture<QueryAuthRequestType, QueryAuthResponseType> queryAuthAdminWaitFuture = null;
        WaitFuture<CheckAuthRequestType, CheckAuthResponseType> checkAuthWaitFuture = null;
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> generalSearchAccountInfoWaitFuture = null;
        WaitFuture<CustomGrayRequestType, CustomGrayResponseType> customGrayWaitFuture = null;
        WaitFuture<ProductInfoInitRequestType, ProductInfoInitResponseType> productInfoInitTask = null;
        WaitFuture<CheckCustomerCbkPermissionRequestType, CheckCustomerCbkPermissionResponseType> checkCustomerCbkPermissionWaitFuture = null;
        WaitFuture<BrandInfoInitRequestType, BrandInfoInitResponseType> brandInfoInitWaitFuture =
                handlerOfBrandInfoInit.handleAsync(mapperOfBrandInfoInitRequest.map(Tuple2.of(
                        Optional.ofNullable(request).map(NavHeaderInitRequestVO::getRequestHeader).orElse(null),
                        Optional.ofNullable(request).map(NavHeaderInitRequestVO::getBrandScene).orElse(null))));
        WaitFuture<MyProfileMenuInitRequestType, MyProfileMenuInitResponseType> myProfileMenuInitWaitFuture = null;
        WaitFuture<ImTripInitRequestType, ImTripInitResponseType> imTripInitWaitFuture = null;
        WaitFuture<QueryApproveTaskRequestType, QueryApproveTaskResponseType> queryApproveTaskWaitFuture = null;
        if (checkRequest) {
            getCorpInfoWaitFuture = handlerOfGetCorpInfo.handleAsync(
                    mapperOfSoaGetCorpInfoRequest.map(Tuple1.of(request.getRequestHeader().getHeader().getCorpId())));
            getCorpUserInfoWaitFuture = handlerOfGetCorpUserInfo.handleAsync(
                    mapperOfGetCorpUserInfoRequest.map(Tuple1.of(request.getRequestHeader())));
            queryAuthReportWaitFuture = handlerOfQueryAuth.handleAsync(
                    mapperOfQueryAuthRequest.map(Tuple2.of(request.getRequestHeader(), ONLINE_REPORT)));
            queryAuthAdminWaitFuture = handlerOfQueryAuth.handleAsync(
                    mapperOfQueryAuthRequest.map(Tuple2.of(request.getRequestHeader(), BLUESPACE_ADMIN)));
            checkAuthWaitFuture = handlerOfCheckAuth.handleAsync(
                    mapperOfCheckAuthRequest.map(Tuple1.of(request.getRequestHeader())));
            generalSearchAccountInfoWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
                    mapperofSoaGeneralSearchAccountInfoRequest.map(Tuple1.of(request.getRequestHeader().getHeader().getUserId())));
            customGrayWaitFuture = handlerOfCustomGray.handleAsync(
                    mapperOfCustomGrayRequest.map(Tuple2.of(request.getRequestHeader(),
                            Lists.newArrayList(NavHeaderInitConstant.BIZ_APPROVAL_ENDORSEMENT_SWITCH, APPROVAL_PERMISSION_NEW))));
            productInfoInitTask = handlerOfProductInfoInit.handleAsync(
                    mapperOfProductInfoInitRequest.map(Tuple1.of(request.getRequestHeader())));
            checkCustomerCbkPermissionWaitFuture = handlerOfCheckCustomerCbkPermission.handleAsync(
                    mapperOfCheckCustomerCbkPermissionRequest.map(Tuple1.of(request.getRequestHeader())));
            myProfileMenuInitWaitFuture = handlerOfMyProfileMenuInit.handleAsync(
                    mapperOfMyProfileMenuInitRequestType.map(Tuple1.of(request.getRequestHeader())));
            imTripInitWaitFuture = handlerOfImTripInit.handleAsync(
                    mapperOfImTripInitRequest.map(Tuple1.of(request)));
            queryApproveTaskWaitFuture = handlerOfQueryApproveTask.handleAsync(
                    mapperOfQueryApproveTaskRequestType.map(Tuple1.of(request.getRequestHeader())));
        }

        NavHeaderInitResponse navHeaderInitResponse = NavHeaderInitResponse.builder()
                .setGetCorpInfoResponseType(Optional.ofNullable(getCorpInfoWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setGetCorpUserInfoResponseType(Optional.ofNullable(getCorpUserInfoWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setQueryAuthResponseType(Optional.ofNullable(queryAuthReportWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setCheckAuthResponseType(Optional.ofNullable(checkAuthWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setGeneralSearchAccountInfoResponseType(Optional.ofNullable(generalSearchAccountInfoWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setCustomGrayResponseType(Optional.ofNullable(customGrayWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setProductInfoInitResponseType(Optional.ofNullable(productInfoInitTask).map(WaitFuture::getWithoutError).orElse(null))
                .setCheckCustomerCbkPermissionResponseType(Optional.ofNullable(checkCustomerCbkPermissionWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setBrandInfoInitResponseType(Optional.ofNullable(brandInfoInitWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setMyProfileMenuInitResponseType(Optional.ofNullable(myProfileMenuInitWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setImTripInitResponse(Optional.ofNullable(imTripInitWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setCheckRequest(checkRequest)
                .setNavHeaderInitRequestVO(request)
                .setQueryApproveTaskResponseType(Optional.ofNullable(queryApproveTaskWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .setQueryAuthAdminResponseType(Optional.ofNullable(queryAuthAdminWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
                .build();
        return mapperOfNavHeaderInitResponseVO.map(Tuple1.of(navHeaderInitResponse));
    }

    private boolean checkRequest(NavHeaderInitRequestVO request) {
        if (request == null
                || request.getRequestHeader() == null
                || request.getRequestHeader().getHeader() == null
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getUserId())
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getCorpId())) {
            return false;
        }
        return true;
    }

    @Override
    public Map<String, String> tracking(NavHeaderInitRequestVO request, NavHeaderInitResponseVO response) {
        return null;
    }
}
