package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient;

import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.basic.home.contract.AnnouncementQueryRequestType;
import com.ctrip.corp.bff.basic.home.contract.AnnouncementQueryResponseType;
import com.ctrip.corp.bff.basic.home.contract.CorpBffBasicHomeServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;

/**
 * <AUTHOR>
 * @date 2024/5/24 15:58
 */

@Component
public class HandlerOfAnnouncementQuery extends
    AbstractHandlerOfSOA<AnnouncementQueryRequestType, AnnouncementQueryResponseType, CorpBffBasicHomeServiceClient> {
    @Override
    protected String getMethodName() {
        return "announcementQuery";
    }
}
