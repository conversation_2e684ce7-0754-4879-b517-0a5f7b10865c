package com.ctrip.corp.bff.basic.home.trip.service;

import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.ConfigInfoQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.processor.ProcessorOfConfigInfoQuery;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 查询配置类信息，法务文档、隐私协议、语言列表等配置类信息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@WebService(name = "configInfoQuery")
public class ServiceOfConfigInfoQuery extends AbstractSyncService<ConfigInfoQueryRequestVO, ConfigInfoQueryResponseVO> {

    @Autowired
    private ProcessorOfConfigInfoQuery processor;

    @Override
    public void validateRequest(ConfigInfoQueryRequestVO requestVO) throws BusinessException {

    }

    @Override
    protected Processor<ConfigInfoQueryRequestVO, ConfigInfoQueryResponseVO> getProcessor(
        ConfigInfoQueryRequestVO requestVO) {
        return processor;
    }
}
