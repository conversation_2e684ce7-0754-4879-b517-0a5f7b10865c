package com.ctrip.corp.bff.basic.home.trip.qconfig.hostdocument;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/22 20:47
 */
public class HostDocumentBO {

    private List<String> hostList;

    // 隐私协议
    private List<StatementBO> privacyStatementList;

    // 法务文档
    private List<StatementBO> termsAndConditionList;

    public List<String> getHostList() {
        return hostList;
    }

    public void setHostList(List<String> hostList) {
        this.hostList = hostList;
    }

    public List<StatementBO> getPrivacyStatementList() {
        return privacyStatementList;
    }

    public void setPrivacyStatementList(List<StatementBO> privacyStatementList) {
        this.privacyStatementList = privacyStatementList;
    }

    public List<StatementBO> getTermsAndConditionList() {
        return termsAndConditionList;
    }

    public void setTermsAndConditionList(List<StatementBO> termsAndConditionList) {
        this.termsAndConditionList = termsAndConditionList;
    }
}
