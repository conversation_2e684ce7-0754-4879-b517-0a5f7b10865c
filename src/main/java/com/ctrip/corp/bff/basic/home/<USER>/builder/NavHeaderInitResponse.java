package com.ctrip.corp.bff.basic.home.trip.builder;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.contract.NavHeaderInitRequestVO;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import corp.settlement.settings.uncore.application.customer.CheckCustomerCbkPermissionResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.authorityManage.CheckAuthResponseType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/9 11:17
 * @Version 1.0
 */
public class NavHeaderInitResponse {
    private GetCorpInfoResponseType getCorpInfoResponseType;
    private GetCorpUserInfoResponseType getCorpUserInfoResponseType;
    private QueryAuthResponseType queryAuthResponseType;
    private CheckAuthResponseType checkAuthResponseType;
    private GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType;
    private CustomGrayResponseType customGrayResponseType;
    private ProductInfoInitResponseType productInfoInitResponseType;
    private CheckCustomerCbkPermissionResponseType checkCustomerCbkPermissionResponseType;
    private Boolean checkRequest;
    private NavHeaderInitRequestVO navHeaderInitRequestVO;
    private BrandInfoInitResponseType brandInfoInitResponseType;
    private MyProfileMenuInitResponseType myProfileMenuInitResponseType;
    private ImTripInitResponseType imTripInitResponse;
    private QueryApproveTaskResponseType queryApproveTaskResponseType;
    private QueryAuthResponseType queryAuthAdminResponseType;

    public GetCorpInfoResponseType getGetCorpInfoResponseType() {
        return getCorpInfoResponseType;
    }

    public GetCorpUserInfoResponseType getGetCorpUserInfoResponseType() {
        return getCorpUserInfoResponseType;
    }

    public QueryAuthResponseType getQueryAuthResponseType() {
        return queryAuthResponseType;
    }

    public CheckAuthResponseType getCheckAuthResponseType() {
        return checkAuthResponseType;
    }

    public GeneralSearchAccountInfoResponseType getGeneralSearchAccountInfoResponseType() {
        return generalSearchAccountInfoResponseType;
    }

    public CustomGrayResponseType getCustomGrayResponseType() {
        return customGrayResponseType;
    }

    public ProductInfoInitResponseType getProductInfoInitResponseType() {
        return productInfoInitResponseType;
    }

    public CheckCustomerCbkPermissionResponseType getCheckCustomerCbkPermissionResponseType() {
        return checkCustomerCbkPermissionResponseType;
    }

    public Boolean getCheckRequest() {
        return checkRequest;
    }

    public NavHeaderInitRequestVO getNavHeaderInitRequestVO() {
        return navHeaderInitRequestVO;
    }

    public BrandInfoInitResponseType getBrandInfoInitResponseType() {
        return brandInfoInitResponseType;
    }

    public MyProfileMenuInitResponseType getMyProfileMenuInitResponseType() {
        return myProfileMenuInitResponseType;
    }

    public ImTripInitResponseType getImTripInitResponse() {
        return imTripInitResponse;
    }

    public QueryApproveTaskResponseType getQueryApproveTaskResponseType() {
        return queryApproveTaskResponseType;
    }

    public QueryAuthResponseType getQueryAuthAdminResponseType() {
        return queryAuthAdminResponseType;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private GetCorpInfoResponseType getCorpInfoResponseType;
        private GetCorpUserInfoResponseType getCorpUserInfoResponseType;
        private QueryAuthResponseType queryAuthResponseType;
        private CheckAuthResponseType checkAuthResponseType;
        private GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType;
        private CustomGrayResponseType customGrayResponseType;
        private ProductInfoInitResponseType productInfoInitResponseType;
        private CheckCustomerCbkPermissionResponseType checkCustomerCbkPermissionResponseType;
        private Boolean checkRequest;
        private NavHeaderInitRequestVO navHeaderInitRequestVO;
        private BrandInfoInitResponseType brandInfoInitResponseType;
        private MyProfileMenuInitResponseType myProfileMenuInitResponseType;
        private ImTripInitResponseType imTripInitResponse;
        private QueryApproveTaskResponseType queryApproveTaskResponseType;
        private QueryAuthResponseType queryAuthAdminResponseType;

        public Builder setGetCorpInfoResponseType(GetCorpInfoResponseType getCorpInfoResponseType) {
            this.getCorpInfoResponseType = getCorpInfoResponseType;
            return this;
        }

        public Builder setGetCorpUserInfoResponseType(GetCorpUserInfoResponseType getCorpUserInfoResponseType) {
            this.getCorpUserInfoResponseType = getCorpUserInfoResponseType;
            return this;
        }

        public Builder setQueryAuthResponseType(QueryAuthResponseType queryAuthResponseType) {
            this.queryAuthResponseType = queryAuthResponseType;
            return this;
        }

        public Builder setCheckAuthResponseType(CheckAuthResponseType checkAuthResponseType) {
            this.checkAuthResponseType = checkAuthResponseType;
            return this;
        }

        public Builder setGeneralSearchAccountInfoResponseType(GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
            this.generalSearchAccountInfoResponseType = generalSearchAccountInfoResponseType;
            return this;
        }

        public Builder setCustomGrayResponseType(CustomGrayResponseType customGrayResponseType) {
            this.customGrayResponseType = customGrayResponseType;
            return this;
        }

        public Builder setProductInfoInitResponseType(ProductInfoInitResponseType productInfoInitResponseType) {
            this.productInfoInitResponseType = productInfoInitResponseType;
            return this;
        }

        public Builder setCheckCustomerCbkPermissionResponseType(CheckCustomerCbkPermissionResponseType checkCustomerCbkPermissionResponseType) {
            this.checkCustomerCbkPermissionResponseType = checkCustomerCbkPermissionResponseType;
            return this;
        }

        public Builder setCheckRequest(Boolean checkRequest) {
            this.checkRequest = checkRequest;
            return this;
        }

        public Builder setNavHeaderInitRequestVO(NavHeaderInitRequestVO navHeaderInitRequestVO) {
            this.navHeaderInitRequestVO = navHeaderInitRequestVO;
            return this;
        }

        public Builder setBrandInfoInitResponseType(BrandInfoInitResponseType brandInfoInitResponseType) {
            this.brandInfoInitResponseType = brandInfoInitResponseType;
            return this;
        }

        public Builder setMyProfileMenuInitResponseType(MyProfileMenuInitResponseType myProfileMenuInitResponseType) {
            this.myProfileMenuInitResponseType = myProfileMenuInitResponseType;
            return this;
        }

        public Builder setImTripInitResponse(ImTripInitResponseType imTripInitResponse) {
            this.imTripInitResponse = imTripInitResponse;
            return this;
        }

        public Builder setQueryApproveTaskResponseType(QueryApproveTaskResponseType queryApproveTaskResponseType) {
            this.queryApproveTaskResponseType = queryApproveTaskResponseType;
            return this;
        }

        public Builder setQueryAuthAdminResponseType(QueryAuthResponseType queryAuthAdminResponseType) {
            this.queryAuthAdminResponseType = queryAuthAdminResponseType;
            return this;
        }

        public NavHeaderInitResponse build() {
            NavHeaderInitResponse navHeaderInitResponse = new NavHeaderInitResponse();
            navHeaderInitResponse.getCorpInfoResponseType = this.getCorpInfoResponseType;
            navHeaderInitResponse.getCorpUserInfoResponseType = this.getCorpUserInfoResponseType;
            navHeaderInitResponse.queryAuthResponseType = this.queryAuthResponseType;
            navHeaderInitResponse.checkAuthResponseType = this.checkAuthResponseType;
            navHeaderInitResponse.generalSearchAccountInfoResponseType = this.generalSearchAccountInfoResponseType;
            navHeaderInitResponse.customGrayResponseType = this.customGrayResponseType;
            navHeaderInitResponse.productInfoInitResponseType = this.productInfoInitResponseType;
            navHeaderInitResponse.checkCustomerCbkPermissionResponseType = this.checkCustomerCbkPermissionResponseType;
            navHeaderInitResponse.checkRequest = this.checkRequest;
            navHeaderInitResponse.navHeaderInitRequestVO = this.navHeaderInitRequestVO;
            navHeaderInitResponse.brandInfoInitResponseType = this.brandInfoInitResponseType;
            navHeaderInitResponse.myProfileMenuInitResponseType = this.myProfileMenuInitResponseType;
            navHeaderInitResponse.imTripInitResponse = this.imTripInitResponse;
            navHeaderInitResponse.queryApproveTaskResponseType = this.queryApproveTaskResponseType;
            navHeaderInitResponse.queryAuthAdminResponseType = this.queryAuthAdminResponseType;
            return navHeaderInitResponse;
        }
    }
}
