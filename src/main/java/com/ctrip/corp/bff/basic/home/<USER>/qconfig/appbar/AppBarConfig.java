package com.ctrip.corp.bff.basic.home.trip.qconfig.appbar;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
public class AppBarConfig {

    private Map<String, List<AppBarInfo>> oldVersionAppBar;

    private Map<String, List<AppBarInfo>> newVersionAppBar;

    public Map<String, List<AppBarInfo>> getOldVersionAppBar() {
        return oldVersionAppBar;
    }

    public void setOldVersionAppBar(Map<String, List<AppBarInfo>> oldVersionAppBar) {
        this.oldVersionAppBar = oldVersionAppBar;
    }

    public Map<String, List<AppBarInfo>> getNewVersionAppBar() {
        return newVersionAppBar;
    }

    public void setNewVersionAppBar(Map<String, List<AppBarInfo>> newVersionAppBar) {
        this.newVersionAppBar = newVersionAppBar;
    }
}
