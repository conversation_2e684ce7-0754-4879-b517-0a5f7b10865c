package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappbarinit.MapperOfAppBarInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.AppBarConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appbar.HostSiteConfig;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.APP_NEW_VERSION;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Component
public class ProcessorOfAppBarInit extends AbstractProcessor<AppBarInitRequestVO, AppBarInitResponseVO> {

    @Autowired
    private HandlerOfCustomGray handlerOfCustomGray;
    @Autowired
    private MapperOfCustomGrayRequest mapperOfCustomGrayRequest;

    @Autowired
    private MapperOfAppBarInitResponseVO mapperOfAppBarInitResponseVO;

    @QConfig("appBarConfig.json")
    private AppBarConfig appBarConfig;

    @QConfig("hostSiteConfig.json")
    private HostSiteConfig hostSiteConfig;

    @Override
    public AppBarInitResponseVO execute(AppBarInitRequestVO request) throws Exception {
        // 灰度信息
        CustomGrayRequestType customGrayRequestType =
                mapperOfCustomGrayRequest.map(Tuple2.of(request.getRequestHeader(),
                        Lists.newArrayList(APP_NEW_VERSION)));
        WaitFuture<CustomGrayRequestType, CustomGrayResponseType> customGrayWaitFuture =
                handlerOfCustomGray.handleAsync(customGrayRequestType);

        return mapperOfAppBarInitResponseVO.map(Tuple4.of(request.getRequestHeader(),
                customGrayWaitFuture.getWithoutError(), appBarConfig, hostSiteConfig));
    }

    @Override
    public Map<String, String> tracking(AppBarInitRequestVO request, AppBarInitResponseVO response) {
        return null;
    }
}
