package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient;

import com.ctrip.corp.bff.basic.home.trip.common.constant.GeneralSearchAccountInfoField;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @Description
 * @author:  mmt
 * @Date: 2024/6/3
 */
@Component
public class MapperofSoaGeneralSearchAccountInfoRequest extends AbstractMapper<Tuple1<String>, GeneralSearchAccountInfoRequestType> {

    @Override
    protected GeneralSearchAccountInfoRequestType convert(Tuple1<String> tuple1) {
        String uid = tuple1.getT1();
        GeneralSearchAccountInfoRequestType requestTO = new GeneralSearchAccountInfoRequestType();
        List<String> searchFields = new ArrayList<>();
        searchFields.add(GeneralSearchAccountInfoField.PLCS_FLIGHT_N);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_FLIGHT_I);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_HOTLE);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_TRAIN);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_TRAIN_I);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_ONCALL);
        searchFields.add(GeneralSearchAccountInfoField.PLCS_CAR_AIRPORT);
        searchFields.add(GeneralSearchAccountInfoField.SHIELD_ONLINE_APP_LANGUAGE);
        searchFields.add(GeneralSearchAccountInfoField.BILL_CONTROL_MODE);
        searchFields.add(GeneralSearchAccountInfoField.BILL_TYPE);
        searchFields.add(GeneralSearchAccountInfoField.IS_SEN_ITINERARY);
        requestTO.setUid(uid);
        requestTO.setSearchFields(searchFields);
        requestTO.setRid(UUID.randomUUID().toString().toUpperCase().replace("-", ""));
        return requestTO;
    }

    @Override
    protected ParamCheckResult check(Tuple1<String> tuple) {
        return null;
    }



}
