package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitResponseVO;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.BrandInfoInitResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 品牌logo processor
 *
 * @author:  renyiwang
 * @Date: 2024/6/14
 */
@Component
public class ProcessorOfBrandInfoInit extends AbstractProcessor<BrandInfoInitRequestVO, BrandInfoInitResponseVO> {

    @Autowired
    private HandlerOfBrandInfoInit handlerOfBrandInfoInit;

    @Autowired
    private MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest;
    @Autowired
    private MapperOfBrandInfoInitResponseVO mapperOfBrandInfoInitResponseVO;


    @Override
    public BrandInfoInitResponseVO execute(BrandInfoInitRequestVO request) {

        if (Objects.isNull(request)) {
            return new BrandInfoInitResponseVO();
        }
        BrandInfoInitResponseType brandInfoInitResponseType =
                handlerOfBrandInfoInit.handleAsync(mapperOfBrandInfoInitRequest.map(Tuple2.of(request.getRequestHeader(), request.getScene()))).get();

        return mapperOfBrandInfoInitResponseVO.map(Tuple1.of(brandInfoInitResponseType));
    }

    @Override
    public Map<String, String> tracking(BrandInfoInitRequestVO request, BrandInfoInitResponseVO response) {
        return null;
    }
}
