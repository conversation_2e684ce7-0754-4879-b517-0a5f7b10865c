package com.ctrip.corp.bff.basic.home.trip.qconfig.apphome;

/**
 * <AUTHOR>
 * @date 2025/3/2
 */
public class AppHomeInfo {

    private String code;

    private String icon;

    private String url;

    private String unreadMessageIcon;

    private Integer sort;

    private String publicBackgroundUrl;

    private String privateBackGroundUrl;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUnreadMessageIcon() {
        return unreadMessageIcon;
    }

    public void setUnreadMessageIcon(String unreadMessageIcon) {
        this.unreadMessageIcon = unreadMessageIcon;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getPublicBackgroundUrl() {
        return publicBackgroundUrl;
    }

    public void setPublicBackgroundUrl(String publicBackgroundUrl) {
        this.publicBackgroundUrl = publicBackgroundUrl;
    }

    public String getPrivateBackGroundUrl() {
        return privateBackGroundUrl;
    }

    public void setPrivateBackGroundUrl(String privateBackGroundUrl) {
        this.privateBackGroundUrl = privateBackGroundUrl;
    }
}
