package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LegalDocumentQueryResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfIpInfoQuery;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfIpInfoQueryRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflegaldocumentquery.MapperOfLegalDocumentResponse;
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigItem;
import com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument.LegalDocumentConfigService;
import com.ctrip.corp.bff.framework.template.common.defalut.LogDefaultUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.tools.contract.IpInfo;
import com.ctrip.corp.bff.tools.contract.IpInfoQueryRequestType;
import com.ctrip.corp.bff.tools.contract.IpInfoQueryResponseType;
import com.google.common.collect.Lists;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description
 * @author: mmt
 * @Date: 2024/8/6
 */
@Component
public class ProcessorOflegalDocumentQuery extends AbstractProcessor<LegalDocumentQueryRequestVO, LegalDocumentQueryResponseVO> {

    @Autowired
    private LegalDocumentConfigService legalDocumentConfigService;
    @Autowired
    private MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest;
    @Autowired
    private HandlerOfGetCorpInfo handlerOfGetCorpInfo;
    @Autowired
    private MapperOfLegalDocumentResponse mapperOfLegalDocumentResponse;
    @Autowired
    private MapperOfIpInfoQueryRequest mapperOfGetIpInfoRequest;
    @Autowired
    private HandlerOfIpInfoQuery handlerOfIpInfoQuery;

    @Override
    public LegalDocumentQueryResponseVO execute(LegalDocumentQueryRequestVO request) {

        // 公司id
        String corpId = Optional.ofNullable(request)
                .map(LegalDocumentQueryRequestVO::getRequestHeader)
                .map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getCorpId)
                .orElse(null);

        String countryCode = null;
        if (Objects.nonNull(request) && StringUtil.isBlank(corpId)) {
            IpInfoQueryRequestType soaRequest = mapperOfGetIpInfoRequest.map(Tuple1.of(request.getRequestHeader()));
            IpInfoQueryResponseType ipInfoQueryResponseType = handlerOfIpInfoQuery.handleAsync(soaRequest).get();
            countryCode = Optional.ofNullable(ipInfoQueryResponseType).map(IpInfoQueryResponseType::getIpInfo).map(IpInfo::getCountryCode).orElse(null);

        }

        // 法务文档配置
        List<LegalDocumentConfigItem> legalDocumentConfigItemList =
                Optional.ofNullable(legalDocumentConfigService.getLegalDocumentConfig())
                        .map(LegalDocumentConfig::getLegalDocumentList)
                        .orElseGet(() -> LogDefaultUtil.bizDefaultVal(Lists.newArrayList(),
                                "LegalDocumentQueryService.legalDocumentQuery", "getLegalDocumentListError"));

        GetCorpInfoResponseType getCorpInfoResponseType = null;
        if (StringUtil.isNotBlank(corpId)) {
            GetCorpInfoRequestType getCorpInfoRequestType = mapperOfSoaGetCorpInfoRequest.map(Tuple1.of(corpId));
            getCorpInfoResponseType = handlerOfGetCorpInfo.handleAsync(getCorpInfoRequestType).get();
        }

        return mapperOfLegalDocumentResponse.map(Tuple4.of(request, legalDocumentConfigItemList, getCorpInfoResponseType, countryCode));
    }


    @Override
    public Map<String, String> tracking(LegalDocumentQueryRequestVO request, LegalDocumentQueryResponseVO response) {
        return null;
    }
}
