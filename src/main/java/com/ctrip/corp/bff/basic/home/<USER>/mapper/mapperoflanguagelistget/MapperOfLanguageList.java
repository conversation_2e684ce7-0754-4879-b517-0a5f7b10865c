package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget;

import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant;
import com.ctrip.corp.bff.basic.home.trip.common.constant.GeneralSearchAccountInfoField;
import com.ctrip.corp.bff.basic.home.trip.common.constant.GetCorpInfoRequestField;
import com.ctrip.corp.bff.basic.home.trip.common.shark.LanguageShark;
import com.ctrip.corp.bff.basic.home.trip.contract.LanguageInfoVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryRequestVO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.languagelist.LanguageConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.languagelist.LanguageInfo;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadContextUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.util.Null;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.corp4jservice.CorpColSetKeyValue;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @author: mmt
 * @Date: 2024/6/3
 */
@Component
public class MapperOfLanguageList extends
    AbstractMapper<Tuple5<TemplateSoaRequestType, GetCorpInfoResponseType, GeneralSearchAccountInfoResponseType, String, GetCorpUserInfoResponseType>, List<LanguageInfoVO>> {

    public static final String BOOKING_USER = "BOOKING_USER";
    /**
     * 支持的语言配置
     */
    @QConfig("*********#languageConfig.json")
    private LanguageConfig languageConfig;

    @Override
    protected List<LanguageInfoVO> convert(
        Tuple5<TemplateSoaRequestType, GetCorpInfoResponseType, GeneralSearchAccountInfoResponseType, String, GetCorpUserInfoResponseType> tuple4) {
        String scene = tuple4.getT4();
        // 优先使用自定义场景去取语言
        if (isCustomScene(scene)) {
            List<LanguageInfoVO> languageInfoVOList = buildLanguageByScene(scene);
            if (CollectionUtil.isNotEmpty(languageInfoVOList)) {
                return languageInfoVOList;
            }
        }

        String corpId = Optional.ofNullable(tuple4.getT1())
            .map(TemplateSoaRequestType::getHeader)
            .map(TemplateHeader::getCorpId)
            .orElse(null);

        GetCorpInfoResponseType getCorpInfoResponse = tuple4.getT2();
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = tuple4.getT3();
        GetCorpUserInfoResponseType getCorpUserInfoResponse = tuple4.getT5();
        // 偏好语言
        String preferredLanguage = buildPreferredLanguage(getCorpUserInfoResponse, scene);
        // 公司配置的pos
        String pos = Optional.ofNullable(getCorpInfoResponse).map(GetCorpInfoResponseType::getPos).orElse(null);
        // 无登录态,根据host匹配语言列表
        if (StringUtil.isBlank(pos)) {
            String gatewayHost = Optional.ofNullable(tuple4.getT1())
                .map(TemplateSoaRequestType::getGatewayHost)
                .orElse(null);

            return getLanguageInfoByHost(gatewayHost, preferredLanguage);
        }
        String shieldLanguage = getShieldLanguage(generalSearchAccountInfoResponseType);
        String customerBrand = getCorpColSetValue(getCorpInfoResponse, GetCorpInfoRequestField.CORP_CUSTOMER_BRAND);
        return getLanguageInfoByPos(pos, shieldLanguage, corpId, customerBrand, preferredLanguage);
    }

    private List<LanguageInfoVO> buildLanguageByScene(String scene) {
        if (Objects.isNull(languageConfig) || CollectionUtil.isEmpty(languageConfig.getCustomSceneMap())) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "config error", "languageConfig error", new HashMap<>());
            return Collections.emptyList();
        }

        List<LanguageInfo> languageInfoList = languageConfig.getCustomSceneMap().getOrDefault(scene, new ArrayList<>());
        return buildLanguageInfoList(languageInfoList, null);

    }

    @Override
    protected ParamCheckResult check(
        Tuple5<TemplateSoaRequestType, GetCorpInfoResponseType, GeneralSearchAccountInfoResponseType, String, GetCorpUserInfoResponseType> tuple) {
        return null;
    }

    /**
     *
     * @param scene:TRIP_BIZ_ALL,TM_ALL, BST_ALL scene传值,优先级最高,如果未传或者传值错误,走原逻辑
     *             BOOKING_USER客户自行支付paylink发邮件场景中的给预订卡发送邮件场景
     * @return
     */
    private boolean isCustomScene(String scene) {
        if (StringUtil.isBlank(scene)) {
            return false;
        }
        if (BOOKING_USER.equalsIgnoreCase(scene)) {
            return false;
        }
        return true;
    }

    private String buildPreferredLanguage(GetCorpUserInfoResponseType getCorpUserInfoResponse, String scene) {
        if (StringUtil.isBlank(scene)) {
            return null;
        }
        if (BOOKING_USER.equalsIgnoreCase(scene)) {
            return Optional.ofNullable(getCorpUserInfoResponse).map(GetCorpUserInfoResponseType::getPreferredLanguage)
                .orElse(null);
        }
        return null;
    }


    private List<LanguageInfoVO> getLanguageInfoByHost(String gatewayHost, String preferredLanguage) {
        if (Objects.isNull(languageConfig) || CollectionUtils.isEmpty(languageConfig.getHostConfigMap())) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "config error", "languageConfig error", new HashMap<>());
            return null;
        }
        if (StringUtil.isBlank(gatewayHost)) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "getGatewayHost error", "gatewayHost is null", new HashMap<>());
            return null;
        }
        Map<String, List<LanguageInfo>> posConfigMap = languageConfig.getHostConfigMap();
        if (CollectionUtils.isEmpty(posConfigMap)) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "config error", "languageConfig hostConfigMap error", new HashMap<>());
            return null;
        }
        List<LanguageInfo> languageInfoList = posConfigMap.getOrDefault(gatewayHost, new ArrayList<>());
        return buildLanguageInfoList(languageInfoList, preferredLanguage);
    }

    public List<LanguageInfoVO> getLanguageInfoByPos(String pos, String shieldOnlineAppLanguage, String corpId,
        String customerBrand, String preferredLanguage) {

        List<LanguageInfoVO> languageByBrandList = languageByBrand(customerBrand, preferredLanguage);
        if (CollectionUtil.isNotEmpty(languageByBrandList)) {
            return languageByBrandList;
        }

        // 获取屏蔽语言的映射关系
        shieldOnlineAppLanguage = Null.or(shieldOnlineAppLanguage, StringUtil.EMPTY);
        List<String> shieldList = Arrays.stream(shieldOnlineAppLanguage.split(","))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (Objects.isNull(languageConfig) || CollectionUtils.isEmpty(languageConfig.getPosConfigMap())) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "config error", "languageConfig error", new HashMap<>());
            return null;
        }
        Map<String, List<LanguageInfo>> posConfigMap = languageConfig.getPosConfigMap();
        List<LanguageInfo> languageInfos = posConfigMap.get(pos);
        if (CollectionUtils.isEmpty(languageInfos)) {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "config error", "languageConfig posConfigMap error", new HashMap<>());
            return null;
        }
        List<LanguageInfoVO> languageTypeList = buildLanguageInfoList(languageInfos, preferredLanguage);

        if (CollectionUtil.isNotEmpty(languageTypeList)) {
            languageTypeList
                    .removeIf(languageType -> CollectionUtil.containsIgnoreCase(shieldList, languageType.getLanguage()));
        }

        return languageTypeList;
    }


    public List<LanguageInfoVO> buildLanguageInfoList(List<LanguageInfo> languageInfos, String preferredLanguage) {
        if (CollectionUtils.isEmpty(languageInfos)) {
            return null;
        }
        List<LanguageInfoVO> languageTypeList = new ArrayList<>();
        for (LanguageInfo languageInfo : languageInfos) {
            LanguageInfoVO languageInfoVO = new LanguageInfoVO();
            languageInfoVO.setLanguage(languageInfo.getLanguage());
            if (StringUtil.isBlank(languageInfo.getLanguage())) {
                continue;
            }
            languageInfoVO.setLanguageName(BFFSharkUtil.getSharkValue(LanguageShark.LOCALE_PREFIX
                    + languageInfo.getLanguage().toLowerCase()));
            languageInfoVO.setDefaultChoose(
                BooleanUtil.parseStr(languageInfo.getLanguage().equalsIgnoreCase(preferredLanguage)));
            languageTypeList.add(languageInfoVO);
        }
        return languageTypeList;
    }

    private List<LanguageInfoVO> languageByBrand(String customerBrand, String preferredLanguage) {
        if (StringUtils.isBlank(customerBrand)) {
            return null;
        }
        if (Objects.isNull(languageConfig)) {
            return null;
        }
        Map<String, List<LanguageInfo>> brandConfigMap = languageConfig.getBrandConfigMap();
        if (CollectionUtils.isEmpty(brandConfigMap) || CollectionUtils.isEmpty(brandConfigMap.get(customerBrand))) {
            return null;
        }
        List<LanguageInfo> customerBrandConfig = brandConfigMap.get(customerBrand);
        if (CollectionUtil.isNotEmpty(customerBrandConfig)) {
            return buildLanguageInfoList(customerBrandConfig, preferredLanguage);
        }
        return null;
    }


    /**
     * 获取运营模式和对客品牌
     */
    public String getCorpColSetValue(GetCorpInfoResponseType corpInfo, String requestField) {

        if (Objects.isNull(corpInfo) || org.apache.commons.collections4.CollectionUtils.isEmpty(corpInfo.getCorpColSetKeyValues())) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, this.getClass(),
                    "GetCorpInfoResponse error", "corpColSetKeyValues is null", new HashMap<>());
            return null;
        }
        // 品牌
        Optional<CorpColSetKeyValue> opt = corpInfo.getCorpColSetKeyValues()
                .stream()
                .filter(corpColSetKeyValueTo -> requestField
                        .equalsIgnoreCase(corpColSetKeyValueTo.getCorpColSetKey()))
                .findFirst();
        if (!opt.isPresent()) {
            return null;
        }
        CorpColSetKeyValue corpColSetKeyValueTo = opt.get();
        if (corpColSetKeyValueTo == null) {
            return null;
        }

        return corpColSetKeyValueTo.getCorpColSetValue();

    }

    private String getShieldLanguage(GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseTypeTO) {
        if (Optional.ofNullable(generalSearchAccountInfoResponseTypeTO)
                .map(GeneralSearchAccountInfoResponseType::getResults).isPresent()
                && CollectionUtil.isNotEmpty(generalSearchAccountInfoResponseTypeTO.getResults())) {
            String shieldLanguage = generalSearchAccountInfoResponseTypeTO.getResults()
                    .get(GeneralSearchAccountInfoField.SHIELD_ONLINE_APP_LANGUAGE);
            LogUtil.loggingClogOnly(LogLevelEnum.Info, this.getClass(), "shieldLanguage", shieldLanguage, new HashMap<>());
            return shieldLanguage;
        }
        return null;
    }
}
