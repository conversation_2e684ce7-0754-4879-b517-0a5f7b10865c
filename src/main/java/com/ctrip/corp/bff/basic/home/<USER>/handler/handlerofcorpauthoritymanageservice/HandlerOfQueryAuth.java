package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpauthoritymanageservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.authorityManage.CorpAuthorityManageServiceClient;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/9 19:07
 * @Version 1.0
 */
@Component
public class HandlerOfQueryAuth extends
        AbstractHandlerOfSOA<QueryAuthRequestType, QueryAuthResponseType, CorpAuthorityManageServiceClient> {
    @Override
    protected String getMethodName() {
        return "queryAuth";
    }
}
