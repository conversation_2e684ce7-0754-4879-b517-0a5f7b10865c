package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.CorpBffBasicHomeServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@Component
public class HandlerOfBrandInfoInit extends AbstractHandlerOfSOA<BrandInfoInitRequestType, BrandInfoInitResponseType, CorpBffBasicHomeServiceClient> {

    @Override
    protected String getMethodName() {
        return "brandInfoInit";
    }
}
