package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryCenterAuthParamType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/9 19:09
 * @Version 1.0
 */
@Component
public class MapperOfQueryAuthRequest extends AbstractMapper<Tuple2<TemplateSoaRequestType, String>, QueryAuthRequestType> {

    private static final String AUTH_CENTER = "Auth_Center";
    private static final String UID = "UID";
    private static final String AUTH = "AUTH";

    @Override
    protected QueryAuthRequestType convert(Tuple2<TemplateSoaRequestType, String> tuple) {
        TemplateSoaRequestType templateSoaRequestType = tuple.getT1();
        String system = tuple.getT2();
        QueryAuthRequestType queryAuthRequestType = new QueryAuthRequestType();
        queryAuthRequestType.setType(AUTH_CENTER);
        queryAuthRequestType.setCenterAuthParam(buildQueryCenterAuthParamType(
                templateSoaRequestType.getHeader().getUserId(), templateSoaRequestType.getHeader().getCorpId(), system));
        return queryAuthRequestType;
    }

    private QueryCenterAuthParamType buildQueryCenterAuthParamType(String uid, String corpId, String system) {
        QueryCenterAuthParamType queryCenterAuthParamType = new QueryCenterAuthParamType();
        queryCenterAuthParamType.setPageIndex(1);
        queryCenterAuthParamType.setPageSize(50);
        queryCenterAuthParamType.setUserType(UID);
        queryCenterAuthParamType.setUserValue(uid);
        queryCenterAuthParamType.setSystem(system);
        queryCenterAuthParamType.setCorpId(corpId);
        queryCenterAuthParamType.setScope(AUTH);
        return queryCenterAuthParamType;
    }
    @Override
    protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, String> tuple) {
        return null;
    }
}
