package com.ctrip.corp.bff.basic.home.trip.qconfig.legaldocument;

import java.util.List;

/**
 * 法务文档配置项
 *
 * <AUTHOR> 2024-02-06
 */
public class LegalDocumentConfigItem {

    // pos站点: jp, sg, hk, vn, en-xx
    private String pos;
    /**
     * 登录前host: "biz.trip.com",
     * "www.trip.biz",
     * "hk.trip.biz",
     * "jp.trip.biz",
     * "sg.trip.biz",
     * "vn.trip.biz",
     * "www.tyo-masters.co.jp",
     * "bizint.ct.ctripcorp.com",
     * "bizint.ct.sinaws.tripws.com",
     * "gatewaytest.trip.biz",
     * "kr.trip.biz"
     */
    private List<String> hostList;

    // common, flight, hotel
    private List<String> siteTypeList;
    // booking, personalCenter, login
    private List<String> pageList;
    // 公司id
    private List<String> corpIdList;
    // ja-jp, en-us, zh-cn, zh-hk, vi-vn, ko-kr
    private List<String> languageList;
    // privacyStatement, termsAndConditions
    private String product;
    // 文档内容pdf url
    private String pdfUrl;

    // H5文档内容pdf url
    private String h5PdfUrl;
    // 页面url
    private String pageUrl;

    // 品牌名
    private String customerBrand;
    // 国家二字码
    private List<String> countryCodeList;
    // 联系我们邮箱
    private String contactUsEmail;

    public String getContactUsEmail() {
        return contactUsEmail;
    }

    public void setContactUsEmail(String contactUsEmail) {
        this.contactUsEmail = contactUsEmail;
    }

    public List<String> getCountryCodeList() {
        return countryCodeList;
    }

    public void setCountryCodeList(List<String> countryCodeList) {
        this.countryCodeList = countryCodeList;
    }

    public String getCustomerBrand() {
        return customerBrand;
    }

    public void setCustomerBrand(String customerBrand) {
        this.customerBrand = customerBrand;
    }

    public String getPos() {
        return pos;
    }

    public void setPos(String pos) {
        this.pos = pos;
    }

    public List<String> getHostList() {
        return hostList;
    }

    public void setHostList(List<String> hostList) {
        this.hostList = hostList;
    }

    public List<String> getSiteTypeList() {
        return siteTypeList;
    }

    public void setSiteTypeList(List<String> siteTypeList) {
        this.siteTypeList = siteTypeList;
    }

    public List<String> getPageList() {
        return pageList;
    }

    public void setPageList(List<String> pageList) {
        this.pageList = pageList;
    }

    public List<String> getCorpIdList() {
        return corpIdList;
    }

    public void setCorpIdList(List<String> corpIdList) {
        this.corpIdList = corpIdList;
    }

    public List<String> getLanguageList() {
        return languageList;
    }

    public void setLanguageList(List<String> languageList) {
        this.languageList = languageList;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getPdfUrl() {
        return pdfUrl;
    }

    public void setPdfUrl(String pdfUrl) {
        this.pdfUrl = pdfUrl;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public String getH5PdfUrl() {
        return h5PdfUrl;
    }

    public void setH5PdfUrl(String h5PdfUrl) {
        this.h5PdfUrl = h5PdfUrl;
    }
}
