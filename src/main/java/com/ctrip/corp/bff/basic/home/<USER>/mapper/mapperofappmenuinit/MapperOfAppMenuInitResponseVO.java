package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofappmenuinit;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.contract.GroupInfoVO;
import com.ctrip.corp.bff.basic.contract.MenuInfoVO;
import com.ctrip.corp.bff.basic.contract.MenuUnitInfoVO;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfo;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.common.enums.AppMenuEnum;
import com.ctrip.corp.bff.basic.home.trip.contract.AppMenuInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfNavHeaderInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuGroupInfo;
import com.ctrip.corp.bff.basic.home.trip.qconfig.appmenu.AppMenuInfo;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.im.contract.CountInfo;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.*;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.SharkKeyConstant.APP_MENU_NAME;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MapperOfAppMenuInitResponseVO extends AbstractMapper<Tuple7<BrandInfoInitResponseType,
        ProductInfoInitResponseType, CustomGrayResponseType, GeneralSearchAccountInfoResponseType,
        QueryApproveTaskResponseType, CountInfoQueryResponseType, AppMenuConfig>, AppMenuInitResponseVO> {
    @Override
    protected AppMenuInitResponseVO convert(Tuple7<BrandInfoInitResponseType, ProductInfoInitResponseType,
            CustomGrayResponseType, GeneralSearchAccountInfoResponseType, QueryApproveTaskResponseType,
            CountInfoQueryResponseType, AppMenuConfig> tuple) {
        return buildAppMenuInitResponseVO(tuple.getT1(), tuple.getT2(),
                tuple.getT3(), tuple.getT4(), tuple.getT5(), tuple.getT6(), tuple.getT7());
    }

    @Override
    protected ParamCheckResult check(Tuple7<BrandInfoInitResponseType, ProductInfoInitResponseType,
            CustomGrayResponseType, GeneralSearchAccountInfoResponseType, QueryApproveTaskResponseType,
            CountInfoQueryResponseType, AppMenuConfig> tuple) {
        return null;
    }

    private AppMenuInitResponseVO buildAppMenuInitResponseVO(BrandInfoInitResponseType brandInfoInitResponseType,
                                                             ProductInfoInitResponseType productInfoInitResponseType,
                                                             CustomGrayResponseType customGrayResponseType,
                                                             GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType,
                                                             QueryApproveTaskResponseType queryApproveTaskResponseType,
                                                             CountInfoQueryResponseType countInfoQueryResponseType,
                                                             AppMenuConfig appMenuConfig) {
        AppMenuInitResponseVO response = new AppMenuInitResponseVO();
        Map<String, Integer> countInfoMap = buildCountInfoMap(countInfoQueryResponseType);
        response.setUnreadMessage(String.valueOf(countInfoMap.getOrDefault(UNREAD_MESSAGE, 0)));
        if (brandInfoInitResponseType != null && brandInfoInitResponseType.getBrandInfo() != null) {
            Map<String, String> logoMap = brandInfoInitResponseType.getBrandInfo().getLogoMap();
            response.setLogoUrl(MapUtils.getString(logoMap, LOGO_MAIN, StringUtils.EMPTY));
        }
        appMenuConfig.sort();
        response.setMenuList(buildMenuInfoVO(productInfoInitResponseType, appMenuConfig, customGrayResponseType,
                queryApproveTaskResponseType, countInfoMap, generalSearchAccountInfoResponseType));
        return response;
    }

    private MenuInfoVO buildMenuInfoVO(ProductInfoInitResponseType productInfoInitResponseType,
                                       AppMenuConfig appMenuConfig, CustomGrayResponseType customGrayResponseType,
                                       QueryApproveTaskResponseType queryApproveTaskResponseType,
                                       Map<String, Integer> countInfoMap,
                                       GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
        Map<String, GroupInfoVO> groupInfoMap = Maps.newHashMap();
        List<MenuUnitInfoVO> bookTripMenuList = buildBookTripGroupMenuList(productInfoInitResponseType, appMenuConfig.getMenuList());
        if (CollectionUtils.isNotEmpty(bookTripMenuList)) {
            GroupInfoVO bookTripGroupInfo = new GroupInfoVO();
            bookTripGroupInfo.setGroupCode(AppMenuEnum.BOOK_TRIP.getCode());
            groupInfoMap.put(AppMenuEnum.BOOK_TRIP.getCode(), bookTripGroupInfo);
        }
        List<MenuUnitInfoVO> manageTripList = buildManageTripGroupMenuList(appMenuConfig.getMenuList(), generalSearchAccountInfoResponseType);
        if (CollectionUtils.isNotEmpty(manageTripList)) {
            GroupInfoVO manageTripGroupInfo = new GroupInfoVO();
            manageTripGroupInfo.setGroupCode(AppMenuEnum.MANAGE_TRIP.getCode());
            groupInfoMap.put(AppMenuEnum.MANAGE_TRIP.getCode(), manageTripGroupInfo);
        }
        GroupInfoVO approvalTripGroup = buildApprovalTripGroup(customGrayResponseType, queryApproveTaskResponseType, countInfoMap);
        if (approvalTripGroup != null) {
            groupInfoMap.put(AppMenuEnum.APPROVE_TRIP.getCode(), approvalTripGroup);
        }
        GroupInfoVO travelRequestGroupInfo = buildTravelRequestGroup(customGrayResponseType, generalSearchAccountInfoResponseType);
        if (travelRequestGroupInfo != null) {
            groupInfoMap.put(AppMenuEnum.TRAVEL_REQUEST.getCode(), travelRequestGroupInfo);
        }
        GroupInfoVO myProfileGroupInfo = new GroupInfoVO();
        myProfileGroupInfo.setGroupCode(AppMenuEnum.MY_PROFILE.getCode());
        groupInfoMap.put(AppMenuEnum.MY_PROFILE.getCode(), myProfileGroupInfo);
        List<GroupInfoVO> groupInfoList = buildGroupInfoList(appMenuConfig.getGroupList(), groupInfoMap);
        List<MenuUnitInfoVO> menuUnitInfoList = Lists.newArrayList();
        menuUnitInfoList.addAll(bookTripMenuList);
        menuUnitInfoList.addAll(manageTripList);
        MenuInfoVO menuInfoVO = new MenuInfoVO();
        menuInfoVO.setGroups(groupInfoList);
        menuInfoVO.setMenus(menuUnitInfoList);
        return menuInfoVO;
    }

    private List<GroupInfoVO> buildGroupInfoList(List<AppMenuGroupInfo> appMenuGroupInfoList,
                                                 Map<String, GroupInfoVO> groupInfoMap) {
        List<GroupInfoVO> groupInfoList = Lists.newArrayList();
        for (AppMenuGroupInfo appMenuGroupInfo : appMenuGroupInfoList) {
            if (groupInfoMap.containsKey(appMenuGroupInfo.getCode())) {
                GroupInfoVO groupInfoVO = groupInfoMap.get(appMenuGroupInfo.getCode());
                groupInfoVO.setUrl(appMenuGroupInfo.getUrl());
                groupInfoVO.setName(BFFSharkUtil.getSharkValue(StringUtils.lowerCase(APP_MENU_NAME + appMenuGroupInfo.getCode())));
                groupInfoList.add(groupInfoVO);
            }
        }
        return groupInfoList;
    }

    private GroupInfoVO buildTravelRequestGroup(CustomGrayResponseType customGrayResponseType,
                                                GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(MapUtils.getString(customGrayResponseType.getResults(),
                BIZ_APPROVAL_ENDORSEMENT_SWITCH, BooleanConstant.FALSE), BooleanConstant.TRUE)) {
            return null;
        }
        if (generalSearchAccountInfoResponseType == null || MapUtils.isEmpty(generalSearchAccountInfoResponseType.getResults())) {
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(MapUtils.getString(generalSearchAccountInfoResponseType.getResults(),
                BILL_CONTROL_MODE, StringUtils.EMPTY), BILL_CONTROL_MODE_TRAVEL_REQUEST)) {
            return null;
        }
        GroupInfoVO travelRequestGroupInfo = new GroupInfoVO();
        travelRequestGroupInfo.setGroupCode(AppMenuEnum.TRAVEL_REQUEST.getCode());
        return travelRequestGroupInfo;
    }

    private GroupInfoVO buildApprovalTripGroup(CustomGrayResponseType customGrayResponseType,
                                               QueryApproveTaskResponseType queryApproveTaskResponseType,
                                               Map<String, Integer> countInfoMap) {
        GroupInfoVO approvalTripGroup = new GroupInfoVO();
        approvalTripGroup.setGroupCode(AppMenuEnum.APPROVE_TRIP.getCode());
        approvalTripGroup.setUnreadMessage(String.valueOf(countInfoMap.getOrDefault(NOT_APPROVAL_NUM, 0)));
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return approvalTripGroup;
        }
        Map<String, String> results = customGrayResponseType.getResults();
        if (!StringUtils.equalsIgnoreCase(MapUtils.getString(results, APPROVAL_PERMISSION_NEW, BooleanConstant.FALSE), BooleanConstant.TRUE)) {
            return approvalTripGroup;
        }
        // 命中灰度，走新逻辑，判断是否有权限
        if (queryApproveTaskResponseType == null || BooleanUtils.isNotTrue(queryApproveTaskResponseType.isExistApproveTask())) {
            return null;
        }
        return approvalTripGroup;
    }

    private List<MenuUnitInfoVO> buildManageTripGroupMenuList(List<AppMenuInfo> appMenuInfoList, GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType) {
        Map<String, MenuUnitInfoVO> menuUnitInfoMap = Maps.newHashMap();
        MenuUnitInfoVO upcomingTripMenu = new MenuUnitInfoVO();
        upcomingTripMenu.setCode(AppMenuEnum.UPCOMING_TRIP.getCode());
        menuUnitInfoMap.put(AppMenuEnum.UPCOMING_TRIP.getCode(), upcomingTripMenu);
        MenuUnitInfoVO allBookingMenu = new MenuUnitInfoVO();
        allBookingMenu.setCode(AppMenuEnum.ALL_BOOKING.getCode());
        menuUnitInfoMap.put(AppMenuEnum.ALL_BOOKING.getCode(), allBookingMenu);
        if (MapperOfNavHeaderInitResponseVO.isItineraryModel(generalSearchAccountInfoResponseType)) {
            MenuUnitInfoVO itineraryMenu = new MenuUnitInfoVO();
            itineraryMenu.setCode(AppMenuEnum.ITINERARY.getCode());
            menuUnitInfoMap.put(AppMenuEnum.ITINERARY.getCode(), itineraryMenu);
        } else {
            MenuUnitInfoVO journeyMenu = new MenuUnitInfoVO();
            journeyMenu.setCode(AppMenuEnum.JOURNEY.getCode());
            menuUnitInfoMap.put(AppMenuEnum.JOURNEY.getCode(), journeyMenu);
        }
        return buildMenuInfoList(appMenuInfoList, menuUnitInfoMap);
    }

    private List<MenuUnitInfoVO> buildBookTripGroupMenuList(ProductInfoInitResponseType productInfoInitResponseType,
                                                            List<AppMenuInfo> appMenuInfoList) {
        if (productInfoInitResponseType == null || CollectionUtils.isEmpty(productInfoInitResponseType.getProductInfos())) {
            return Lists.newArrayList();
        }
        Map<String, MenuUnitInfoVO> menuUnitInfoMap = Maps.newHashMap();
        for (ProductInfo productInfo : productInfoInitResponseType.getProductInfos()) {
            if (productInfo == null || !StringUtils.equalsIgnoreCase(productInfo.getShow(), BooleanConstant.TRUE)) {
                continue;
            }
            MenuUnitInfoVO menuUnitInfoVO = new MenuUnitInfoVO();
            menuUnitInfoVO.setCode(StringUtils.lowerCase(productInfo.getProductLine()));
            menuUnitInfoMap.put(menuUnitInfoVO.getCode(), menuUnitInfoVO);
        }
        return buildMenuInfoList(appMenuInfoList, menuUnitInfoMap);
    }

    private List<MenuUnitInfoVO> buildMenuInfoList(List<AppMenuInfo> appMenuInfoList,
                                                   Map<String, MenuUnitInfoVO> menuUnitInfoMap) {
        List<MenuUnitInfoVO> menuUnitInfoList = Lists.newArrayList();
        for (AppMenuInfo appMenuInfo : appMenuInfoList) {
            if (menuUnitInfoMap.containsKey(appMenuInfo.getCode())) {
                MenuUnitInfoVO menuUnitInfoVO = menuUnitInfoMap.get(appMenuInfo.getCode());
                menuUnitInfoVO.setIcon(appMenuInfo.getIcon());
                menuUnitInfoVO.setUrl(appMenuInfo.getUrl());
                menuUnitInfoVO.setGroupCode(appMenuInfo.getGroupCode());
                menuUnitInfoVO.setName(BFFSharkUtil.getSharkValue(StringUtils.lowerCase(APP_MENU_NAME + appMenuInfo.getCode())));
                menuUnitInfoList.add(menuUnitInfoVO);
            }
        }
        return menuUnitInfoList;
    }

    private Map<String, Integer> buildCountInfoMap(CountInfoQueryResponseType countInfoQueryResponseType) {
        if (countInfoQueryResponseType != null && CollectionUtils.isNotEmpty(countInfoQueryResponseType.getCountInfos())) {
            return countInfoQueryResponseType.getCountInfos().stream().filter(countInfo -> {
                if (countInfo == null) {
                    return false;
                }
                if (StringUtils.isBlank(countInfo.getKey())) {
                    return false;
                }
                if (countInfo.getCount() == null) {
                    return false;
                }
                return true;
            }).collect(Collectors.toMap(CountInfo::getKey, CountInfo::getCount, (k1, k2) -> k1));
        }
        return Maps.newHashMap();
    }

}
