package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofannouncementquery;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.QueryBaseEntity;
import com.ctrip.corp.hotel.book.query.entity.UserInfoEntity;
import com.ctrip.framework.foundation.Foundation;

/**
 * <AUTHOR>
 * @Date 2024/4/1 20:48
 * @Version 1.0
 */
@Component
public class MapperOfGetCityBaseInfoRequest extends AbstractMapper<Tuple2<List<String>, TemplateSoaRequestType>, GetCityBaseInfoRequestType> {

    @Override
    protected GetCityBaseInfoRequestType convert(Tuple2<List<String>, TemplateSoaRequestType> tuple) {
        GetCityBaseInfoRequestType getCityBaseInfoRequestType = new GetCityBaseInfoRequestType();
        List<Integer> cityIds = Optional.ofNullable(tuple.getT1()).orElse(new ArrayList<>()).stream().map(TemplateNumberUtil::parseInt).distinct().collect(Collectors.toList());
        getCityBaseInfoRequestType.setCityIdList(cityIds);
        TemplateSoaRequestType header = tuple.getT2();
        String rid = Optional.ofNullable(header).map(TemplateSoaRequestType::getRequestId).orElse(UUID.randomUUID().toString());
        QueryBaseEntity queryBaseEntity = new QueryBaseEntity();
        queryBaseEntity.setTraceId(rid);
        String language = Optional.ofNullable(header).map(TemplateSoaRequestType::getLanguage).orElse(StringUtils.EMPTY);
        queryBaseEntity.setLocale(language);
        String uid = Optional.ofNullable(header).map(TemplateSoaRequestType::getHeader).map(TemplateHeader::getUserId).orElse(StringUtils.EMPTY);
        String corpId = Optional.ofNullable(header).map(TemplateSoaRequestType::getHeader).map(TemplateHeader::getCorpId).orElse(StringUtils.EMPTY);
        queryBaseEntity.setUserInfo(new UserInfoEntity(uid, corpId));
        queryBaseEntity.setRequestFrom(Foundation.app().getAppId());
        String channel = Optional.ofNullable(header).map(TemplateSoaRequestType::getSourceFrom).map(Enum::name).orElse(SourceFrom.H5.name());
        queryBaseEntity.setBookingChannel(channel);
        getCityBaseInfoRequestType.setBaseInfo(queryBaseEntity);
        return getCityBaseInfoRequestType;
    }
    @Override
    protected ParamCheckResult check(Tuple2<List<String>, TemplateSoaRequestType> integerIntegrationSoaRequestTypeTuple2) {
        return null;
    }
}
