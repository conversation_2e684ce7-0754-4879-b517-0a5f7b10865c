package com.ctrip.corp.bff.basic.home.trip.processor;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskRequestType;
import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitRequestType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.common.constant.HomeInitConstant;
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfCountInfoQueryRequestType;
import com.ctrip.corp.bff.basic.home.trip.common.mapper.MapperOfQueryApproveTaskRequestType;
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitRequestVO;
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpapproveserviceclient.HandlerOfQueryApproveTask;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfBrandInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasichomeserviceclient.HandlerOfProductInfoInit;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient.HandlerOfCountInfoQuery;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbfftoolsserviceclient.HandlerOfCustomGray;
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofapphomeinit.MapperOfAppHomeInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofbrandinfoinit.MapperOfBrandInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfCustomGrayRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfGetCorpUserInfoRequest;
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfProductInfoInitRequest;
import com.ctrip.corp.bff.basic.home.trip.qconfig.apphome.AppHomeConfig;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayRequestType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.ctrip.corp.foundation.common.util.Null;
import com.google.common.collect.Lists;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.APPROVAL_PERMISSION_NEW;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.NOT_APPROVAL_NUM;

/**
 * <AUTHOR>
 * @date 2025/2/28
 */
@Component
public class ProcessorOfAppHomeInit extends AbstractProcessor<AppHomeInitRequestVO, AppHomeInitResponseVO> {

    @Autowired
    private HandlerOfProductInfoInit handlerOfProductInfoInit;
    @Autowired
    private MapperOfProductInfoInitRequest mapperOfProductInfoInitRequest;

    @Autowired
    private HandlerOfCustomGray handlerOfCustomGray;
    @Autowired
    private MapperOfCustomGrayRequest mapperOfCustomGrayRequest;

    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;

    @Autowired
    private HandlerOfBrandInfoInit handlerOfBrandInfoInit;
    @Autowired
    private MapperOfBrandInfoInitRequest mapperOfBrandInfoInitRequest;

    @Autowired
    private HandlerOfQueryApproveTask handlerOfQueryApproveTask;
    @Autowired
    private MapperOfQueryApproveTaskRequestType mapperOfQueryApproveTaskRequestType;

    @Autowired
    private HandlerOfCountInfoQuery handlerOfCountInfoQuery;
    @Autowired
    private MapperOfCountInfoQueryRequestType mapperOfCountInfoQueryRequestType;

    @Autowired
    private MapperOfAppHomeInitResponseVO mapperOfAppHomeInitResponseVO;

    @QConfig("appHomeConfig.json")
    private AppHomeConfig appHomeConfig;

    @Override
    public AppHomeInitResponseVO execute(AppHomeInitRequestVO request) throws Exception {
        List<String> expectedDataList = request.getExpectedDataList();
        boolean containAll = CollectionUtil.isEmpty(expectedDataList);
        boolean containLogo = CollectionUtil.containsIgnoreCase(expectedDataList, HomeInitConstant.LOGO_INFO);
        boolean containProductTab = CollectionUtil.containsIgnoreCase(expectedDataList, HomeInitConstant.PRODUCT_TAB);
        WaitFuture<BrandInfoInitRequestType, BrandInfoInitResponseType> brandInfoInitWaitFuture = null;
        WaitFuture<ProductInfoInitRequestType, ProductInfoInitResponseType> productInfoInitWaitFuture = null;
        WaitFuture<CustomGrayRequestType, CustomGrayResponseType> customGrayWaitFuture = null;
        WaitFuture<QueryApproveTaskRequestType, QueryApproveTaskResponseType> queryApproveTaskTypeWaitFuture = null;
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoWaitFuture = null;
        WaitFuture<CountInfoQueryRequestType, CountInfoQueryResponseType> countInfoQueryWaitFuture = null;
        if (containLogo || containAll) {
            // 1、logo
            BrandInfoInitRequestType brandInfoInitRequestType =
                mapperOfBrandInfoInitRequest.map(Tuple2.of(request.getRequestHeader(), null));
            brandInfoInitWaitFuture = handlerOfBrandInfoInit.handleAsync(brandInfoInitRequestType);
        }
        if (containProductTab || containAll) {
            // 2、产线信息
            ProductInfoInitRequestType productInfoInitRequestType =
                mapperOfProductInfoInitRequest.map(Tuple1.of(request.getRequestHeader()));
            productInfoInitWaitFuture = handlerOfProductInfoInit.handleAsync(productInfoInitRequestType);
        }
        if (containAll) {
            // 3、灰度信息
            CustomGrayRequestType customGrayRequestType = mapperOfCustomGrayRequest.map(
                Tuple2.of(request.getRequestHeader(), Lists.newArrayList(APPROVAL_PERMISSION_NEW)));
            customGrayWaitFuture = handlerOfCustomGray.handleAsync(customGrayRequestType);
            // 4、审批权限
            QueryApproveTaskRequestType queryApproveTaskRequestType =
                mapperOfQueryApproveTaskRequestType.map(Tuple1.of(request.getRequestHeader()));
            queryApproveTaskTypeWaitFuture = handlerOfQueryApproveTask.handleAsync(queryApproveTaskRequestType);
            // 5、用户信息
            GetCorpUserInfoRequestType getCorpUserInfoRequestType =
                mapperOfGetCorpUserInfoRequest.map(Tuple1.of(request.getRequestHeader()));
            getCorpUserInfoWaitFuture = handlerOfGetCorpUserInfo.handleAsync(getCorpUserInfoRequestType);
            // 6、未读消息和待审批数量
            CountInfoQueryRequestType countInfoQueryRequestType = mapperOfCountInfoQueryRequestType.map(
                Tuple2.of(request.getRequestHeader(), Lists.newArrayList(NOT_APPROVAL_NUM)));
            countInfoQueryWaitFuture = handlerOfCountInfoQuery.handleAsync(countInfoQueryRequestType);
        }
        return mapperOfAppHomeInitResponseVO.map(
            Tuple8.of(Null.or(brandInfoInitWaitFuture, WaitFuture::getWithoutError),
                Null.or(productInfoInitWaitFuture, WaitFuture::getWithoutError),
                Null.or(customGrayWaitFuture, WaitFuture::getWithoutError),
                Null.or(queryApproveTaskTypeWaitFuture, WaitFuture::getWithoutError),
                Null.or(getCorpUserInfoWaitFuture, WaitFuture::getWithoutError), appHomeConfig,
                request.getRequestHeader(), Null.or(countInfoQueryWaitFuture, WaitFuture::getWithoutError)));
    }

    @Override
    public Map<String, String> tracking(AppHomeInitRequestVO request, AppHomeInitResponseVO response) {
        return null;
    }
}
