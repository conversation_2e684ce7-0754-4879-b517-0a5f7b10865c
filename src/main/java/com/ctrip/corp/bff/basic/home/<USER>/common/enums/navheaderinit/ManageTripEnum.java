package com.ctrip.corp.bff.basic.home.trip.common.enums.navheaderinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Description 导航头manage trip菜单信息
 * @Date: 2024/11/20 16:17
 * @Version 1.0
 */
public enum ManageTripEnum implements NavHeaderEnumInterface {

    /**
     * Upcoming Trips
     */
    UPCOMING_TRIPS("UPCOMING_TRIPS", null, null, "upComingTripsUrl"),

    /**
     * All Bookings
     */
    ALL_BOOKINGS("ALL_BOOKINGS", null, null, "allBookingsUrl"),

    /**
     * Journeys
     */
    JOURNEYS("JOURNEYS", null, null, "journeysUrl"),

    /**
     * Itinerary
     */
    ITINERARY("ITINERARY", null, null, "itineraryUrl"),

    /**
     * Travel Request
     */
    TRAVEL_REQUEST("TRAVEL_REQUEST", null, null, "travelRequestUrl"),

    /**
     * Travel Approval
     */
    TRAVEL_APPROVAL("TRAVEL_APPROVAL", null, null, "travelApprovalUrl"),
    ;

    ManageTripEnum(String code, String icon, GroupEnum group, String url) {
        this.code = code;
        this.icon = icon;
        this.group = group;
        this.url = url;
    }

    /**
     * code
     */
    private final String code;

    /**
     * 图标
     */
    private final String icon;

    /**
     * 分组code
     */
    private final GroupEnum group;

    /**
     * 链接
     */
    private final String url;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public GroupEnum getGroup() {
        return group;
    }



    @Override
    public String getName() {
        return BFFSharkUtil.getSharkValue("trip.biz.common.biz.text.header.manage_trip." + name().toLowerCase());
    }

    @Override
    public String getUrl(Map<String, String> navHeaderUrlConfig) {
        if (MapUtil.isEmpty(navHeaderUrlConfig)) {
            return "";
        }
        return navHeaderUrlConfig.get(url);
    }
}
