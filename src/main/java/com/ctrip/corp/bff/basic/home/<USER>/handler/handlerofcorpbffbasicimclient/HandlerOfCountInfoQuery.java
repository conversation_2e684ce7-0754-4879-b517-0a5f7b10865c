package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffbasicimclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class HandlerOfCountInfoQuery extends AbstractHandlerOfSOA<CountInfoQueryRequestType, CountInfoQueryResponseType, CorpBffBasicImServiceClient> {

    @Override
    protected String getMethodName() {
        return "countInfoQuery";
    }
}
