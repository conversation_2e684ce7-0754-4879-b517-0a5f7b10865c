package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit;

import com.ctrip.corp.bff.basic.home.trip.common.constant.NavHeaderInitConstant;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.authorityManage.CheckAuthRequestType;
import corp.user.service.authorityManage.CheckCenterAuthParamType;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Date: 2024/12/10 14:21
 * @Version 1.0
 */
@Component
public class MapperOfCheckAuthRequest extends AbstractMapper<Tuple1<TemplateSoaRequestType>, CheckAuthRequestType> {

    private static final String RULE = "CHECK_BY_ROOT_WITH_PART_CHILDREN";
    private static final String SYSTEM = "BLUESPACE_ADMIN";
    private static final String TYPE = "Auth_Center";
    private static final String AUTH_TYPE = "UID";

    @Override
    protected CheckAuthRequestType convert(Tuple1<TemplateSoaRequestType> tuple1) {
        CheckAuthRequestType requestType = new CheckAuthRequestType();
        CheckCenterAuthParamType authParam = new CheckCenterAuthParamType();
        requestType.setType(TYPE);
        authParam.setType(AUTH_TYPE);
        authParam.setValue(tuple1.getT1().getHeader().getUserId());
        authParam.setSystem(SYSTEM);
        authParam.setRule(RULE);
        authParam.setCodes(NavHeaderInitConstant.CHECK_AUTH_CODES);
        requestType.setCenterAuthParam(authParam);
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<TemplateSoaRequestType> tuple1) {
        return null;
    }
}
