package com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpbffmcinfoserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.mcinfo.contract.CorpBffMcinfoServiceClient;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitRequestType;
import com.ctrip.corp.bff.mcinfo.contract.MyProfileMenuInitResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component
public class HandlerOfMyProfileMenuInit extends AbstractHandlerOfSOA<MyProfileMenuInitRequestType, MyProfileMenuInitResponseType, CorpBffMcinfoServiceClient> {
    @Override
    protected String getMethodName() {
        return "myProfileMenuInit";
    }
}
