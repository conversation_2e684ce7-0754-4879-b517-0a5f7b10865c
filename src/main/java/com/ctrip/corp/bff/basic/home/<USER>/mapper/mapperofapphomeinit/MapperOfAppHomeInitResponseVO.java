package com.ctrip.corp.bff.basic.home.trip.mapper.mapperofapphomeinit;

import com.ctrip.corp.approve.ws.contract.QueryApproveTaskResponseType;
import com.ctrip.corp.bff.basic.contract.MenuUnitInfoVO;
import com.ctrip.corp.bff.basic.contract.PictureInfoVO;
import com.ctrip.corp.bff.basic.contract.SupportVO;
import com.ctrip.corp.bff.basic.contract.UserInfoVO;
import com.ctrip.corp.bff.basic.home.contract.BrandInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.contract.ProductInfo;
import com.ctrip.corp.bff.basic.home.contract.ProductInfoInitResponseType;
import com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant;
import com.ctrip.corp.bff.basic.home.trip.common.enums.AppMenuEnum;
import com.ctrip.corp.bff.basic.home.trip.contract.AppHomeInitResponseVO;
import com.ctrip.corp.bff.basic.home.trip.contract.LogoInfoVO;
import com.ctrip.corp.bff.basic.home.trip.qconfig.apphome.AppHomeConfig;
import com.ctrip.corp.bff.basic.home.trip.qconfig.apphome.AppHomeInfo;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.im.contract.CountInfo;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.tools.contract.CustomGrayResponseType;
import com.ctrip.corp.foundation.common.util.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ctrip.corp.bff.basic.home.trip.common.constant.CommonConstant.*;
import static com.ctrip.corp.bff.basic.home.trip.common.constant.SharkKeyConstant.*;

/**
 * <AUTHOR>
 * @date 2025/3/2
 */
@Component
public class MapperOfAppHomeInitResponseVO extends AbstractMapper<Tuple8<BrandInfoInitResponseType,
        ProductInfoInitResponseType, CustomGrayResponseType, QueryApproveTaskResponseType,
        GetCorpUserInfoResponseType, AppHomeConfig, TemplateSoaRequestType, CountInfoQueryResponseType>,
        AppHomeInitResponseVO> {
    @Override
    protected AppHomeInitResponseVO convert(Tuple8<BrandInfoInitResponseType, ProductInfoInitResponseType,
            CustomGrayResponseType, QueryApproveTaskResponseType, GetCorpUserInfoResponseType,
            AppHomeConfig, TemplateSoaRequestType, CountInfoQueryResponseType> tuple) {
        return buildAppHomeInitResponseVO(tuple.getT1(), tuple.getT2(), tuple.getT3(),
                tuple.getT4(), tuple.getT5(), tuple.getT6(), tuple.getT7(), tuple.getT8());
    }

    @Override
    protected ParamCheckResult check(Tuple8<BrandInfoInitResponseType, ProductInfoInitResponseType,
            CustomGrayResponseType, QueryApproveTaskResponseType, GetCorpUserInfoResponseType,
            AppHomeConfig, TemplateSoaRequestType, CountInfoQueryResponseType> tuple) {
        return null;
    }

    private AppHomeInitResponseVO buildAppHomeInitResponseVO(BrandInfoInitResponseType brandInfoInitResponseType,
                                                             ProductInfoInitResponseType productInfoInitResponseType,
                                                             CustomGrayResponseType customGrayResponseType,
                                                             QueryApproveTaskResponseType queryApproveTaskResponseType,
                                                             GetCorpUserInfoResponseType getCorpUserInfoResponseType,
                                                             AppHomeConfig appHomeConfig,
                                                             TemplateSoaRequestType templateSoaRequestType,
                                                             CountInfoQueryResponseType countInfoQueryResponseType) {
        appHomeConfig.sort();
        AppHomeInitResponseVO appHomeInitResponseVO = new AppHomeInitResponseVO();
        appHomeInitResponseVO.setUserInfo(buildUserInfo(getCorpUserInfoResponseType));
        appHomeInitResponseVO.setLogoInfo(buildLogoInfoVO(brandInfoInitResponseType, appHomeConfig, getPosEnum(templateSoaRequestType)));
        appHomeInitResponseVO.setProductTabList(buildProductTabList(productInfoInitResponseType, appHomeConfig.getProductTabList()));
        appHomeInitResponseVO.setHomeButtonList(buildHomeButtonList(customGrayResponseType,
                queryApproveTaskResponseType, appHomeConfig.getHomeButtonList(), countInfoQueryResponseType));
        return appHomeInitResponseVO;
    }

    private List<MenuUnitInfoVO> buildHomeButtonList(CustomGrayResponseType customGrayResponseType,
                                                     QueryApproveTaskResponseType queryApproveTaskResponseType,
                                                     List<AppHomeInfo> homeButtonList,
                                                     CountInfoQueryResponseType countInfoQueryResponseType) {
        Map<String, MenuUnitInfoVO> menuUnitInfoMap = Maps.newHashMap();
        // book trips
        MenuUnitInfoVO bookTripsMenuInfo = new MenuUnitInfoVO();
        bookTripsMenuInfo.setCode(AppMenuEnum.BOOK_TRIP.getCode());
        menuUnitInfoMap.put(AppMenuEnum.BOOK_TRIP.getCode(), bookTripsMenuInfo);
        // manage trips
        MenuUnitInfoVO manageTripsMenuInfo = new MenuUnitInfoVO();
        manageTripsMenuInfo.setCode(AppMenuEnum.MANAGE_TRIP.getCode());
        menuUnitInfoMap.put(AppMenuEnum.MANAGE_TRIP.getCode(), manageTripsMenuInfo);
        // approve trips
        MenuUnitInfoVO approveTripMenuInfo = buildApproveTripsMenuInfo(
                customGrayResponseType, queryApproveTaskResponseType, countInfoQueryResponseType);
        if (approveTripMenuInfo != null) {
            menuUnitInfoMap.put(AppMenuEnum.APPROVE_TRIP.getCode(), approveTripMenuInfo);
        }
        return buildMenuInfoList(homeButtonList, menuUnitInfoMap);
    }

    private MenuUnitInfoVO buildApproveTripsMenuInfo(CustomGrayResponseType customGrayResponseType,
                                                     QueryApproveTaskResponseType queryApproveTaskResponseType,
                                                     CountInfoQueryResponseType countInfoQueryResponseType) {
        MenuUnitInfoVO approvalTripMenu = new MenuUnitInfoVO();
        approvalTripMenu.setCode(AppMenuEnum.APPROVE_TRIP.getCode());
        approvalTripMenu.setUnreadMessage(getNotApprovalNum(countInfoQueryResponseType));
        if (customGrayResponseType == null || MapUtils.isEmpty(customGrayResponseType.getResults())) {
            return approvalTripMenu;
        }
        Map<String, String> results = customGrayResponseType.getResults();
        if (!StringUtils.equalsIgnoreCase(MapUtils.getString(results, APPROVAL_PERMISSION_NEW, BooleanConstant.FALSE), BooleanConstant.TRUE)) {
            return approvalTripMenu;
        }
        // 命中灰度，走新逻辑，判断是否有权限
        if (queryApproveTaskResponseType == null || BooleanUtils.isNotTrue(queryApproveTaskResponseType.isExistApproveTask())) {
            return null;
        }
        return approvalTripMenu;
    }

    private String getNotApprovalNum(CountInfoQueryResponseType countInfoQueryResponseType) {
        if (countInfoQueryResponseType == null || CollectionUtils.isEmpty(countInfoQueryResponseType.getCountInfos())) {
            return "0";
        }
        for (CountInfo countInfo : countInfoQueryResponseType.getCountInfos()) {
            if (countInfo == null || countInfo.getCount() == null) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(countInfo.getKey(), NOT_APPROVAL_NUM)) {
                return String.valueOf(countInfo.getCount());
            }
        }
        return "0";
    }

    private List<MenuUnitInfoVO> buildProductTabList(ProductInfoInitResponseType productInfoInitResponseType,
                                                     List<AppHomeInfo> appHomeInfoList) {
        if (productInfoInitResponseType == null || CollectionUtils.isEmpty(productInfoInitResponseType.getProductInfos())) {
            return Lists.newArrayList();
        }
        Map<String, MenuUnitInfoVO> menuUnitInfoMap = Maps.newHashMap();
        for (ProductInfo productInfo : productInfoInitResponseType.getProductInfos()) {
            if (productInfo == null || !StringUtils.equalsIgnoreCase(productInfo.getShow(), BooleanConstant.TRUE)) {
                continue;
            }
            MenuUnitInfoVO menuUnitInfoVO = new MenuUnitInfoVO();
            menuUnitInfoVO.setCode(StringUtils.lowerCase(productInfo.getProductLine()));
            SupportVO supportPublic = new SupportVO();
            supportPublic.setCode(SUPPORT_PUBLIC);
            supportPublic.setName(BFFSharkUtil.getSharkValue(SUPPORT_NAME + supportPublic.getCode()));
            supportPublic.setSupport(productInfo.getSupportPublic());
            SupportVO supportPrivate = new SupportVO();
            supportPrivate.setCode(SUPPORT_PRIVATE);
            supportPrivate.setName(BFFSharkUtil.getSharkValue(SUPPORT_NAME + supportPrivate.getCode()));
            supportPrivate.setSupport(productInfo.getSupportPrivate());
            menuUnitInfoVO.setSupports(Lists.newArrayList(supportPublic, supportPrivate));
            menuUnitInfoMap.put(menuUnitInfoVO.getCode(), menuUnitInfoVO);
        }
        return buildMenuInfoList(appHomeInfoList, menuUnitInfoMap);
    }

    private List<MenuUnitInfoVO> buildMenuInfoList(List<AppHomeInfo> appHomeInfoList,
                                                   Map<String, MenuUnitInfoVO> menuUnitInfoMap) {
        List<MenuUnitInfoVO> menuUnitInfoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(appHomeInfoList)) {
            return menuUnitInfoList;
        }
        for (AppHomeInfo appHomeInfo : appHomeInfoList) {
            if (menuUnitInfoMap.containsKey(appHomeInfo.getCode())) {
                MenuUnitInfoVO menuUnitInfoVO = menuUnitInfoMap.get(appHomeInfo.getCode());
                menuUnitInfoVO.setIcon(appHomeInfo.getIcon());
                menuUnitInfoVO.setUnreadMessageIcon(appHomeInfo.getUnreadMessageIcon());
                menuUnitInfoVO.setUrl(appHomeInfo.getUrl());
                menuUnitInfoVO.setName(BFFSharkUtil.getSharkValue(StringUtils.lowerCase(APP_HOME_BUTTON_NAME + appHomeInfo.getCode())));
                menuUnitInfoList.add(menuUnitInfoVO);
                if (CollectionUtils.isNotEmpty(menuUnitInfoVO.getSupports())) {
                    for (SupportVO support : menuUnitInfoVO.getSupports()) {
                        if (StringUtils.equalsIgnoreCase(support.getCode(), SUPPORT_PUBLIC)) {
                            support.setBackgroundUrl(appHomeInfo.getPublicBackgroundUrl());
                        }
                        if (StringUtils.equalsIgnoreCase(support.getCode(), SUPPORT_PRIVATE)) {
                            support.setBackgroundUrl(appHomeInfo.getPrivateBackGroundUrl());
                        }
                    }
                }
            }
        }
        return menuUnitInfoList;
    }

    private LogoInfoVO buildLogoInfoVO(BrandInfoInitResponseType brandInfoInitResponseType,
                                       AppHomeConfig appHomeConfig, PosEnum posEnum) {
        LogoInfoVO logoInfo = new LogoInfoVO();
        if (brandInfoInitResponseType == null) {
            return logoInfo;
        }
        if (StringUtils.isNotBlank(brandInfoInitResponseType.getFooterLogo())) {
            logoInfo.setLogoType(APP_HOME_LOGO_TYPE_WHITE);
            logoInfo.setLogoUrl(brandInfoInitResponseType.getFooterLogo());
        } else {
            logoInfo.setLogoType(APP_HOME_LOGO_TYPE_NORMAL);
            if (brandInfoInitResponseType.getBrandInfo() != null) {
                Map<String, String> logoMap = brandInfoInitResponseType.getBrandInfo().getLogoMap();
                logoInfo.setLogoUrl(MapUtils.getString(logoMap, LOGO_ANTI_WHITE, StringUtils.EMPTY));
            }
        }
        if (brandInfoInitResponseType.getBrandInfo() != null) {
            logoInfo.setCustomerBrand(brandInfoInitResponseType.getBrandInfo().getCustomerBrand());
        }
        PictureInfoVO all = new PictureInfoVO();
        all.setPictureType(PICTURE_TYPE_ALL);
        all.setPictureUrl(appHomeConfig.getBackgroundUrlAllByPos(posEnum));
        PictureInfoVO half = new PictureInfoVO();
        half.setPictureType(PICTURE_TYPE_HALF);
        half.setPictureUrl(appHomeConfig.getBackgroundUrlHalfByPos(posEnum));
        PictureInfoVO onlyJourney = new PictureInfoVO();
        onlyJourney.setPictureType(PICTURE_TYPE_ONLY_JOURNEY);
        onlyJourney.setPictureUrl(appHomeConfig.getBackgroundUrlOnlyJourneyByPos(posEnum));
        logoInfo.setBackgrounds(Lists.newArrayList(all, half, onlyJourney));
        return logoInfo;
    }

    private UserInfoVO buildUserInfo(GetCorpUserInfoResponseType getCorpUserInfoResponseType) {
        UserInfoVO userInfo = new UserInfoVO();
        if (getCorpUserInfoResponseType == null) {
            return userInfo;
        }
        userInfo.setPreferFirstName(getCorpUserInfoResponseType.getPreferredFirstName());
        userInfo.setPreferLastName(getCorpUserInfoResponseType.getPreferredLastName());
        return userInfo;
    }

    private PosEnum getPosEnum(TemplateSoaRequestType request) {
        return Optional.ofNullable(request)
                .map(TemplateSoaRequestType::getPos)
                .orElse(PosEnum.XX);
    }

}
