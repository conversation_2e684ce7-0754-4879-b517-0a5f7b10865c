package com.ctrip.corp.bff.basic.home.trip.qconfig.appbar;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
public class AppBarInfo {

    private String code;

    private String name;

    private String nameColor;

    private String selectedNameColor;

    private String iconUrl;

    private String selectedIconUrl;

    private String redirectUrl;

    private String sharkKey;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameColor() {
        return nameColor;
    }

    public void setNameColor(String nameColor) {
        this.nameColor = nameColor;
    }

    public String getSelectedNameColor() {
        return selectedNameColor;
    }

    public void setSelectedNameColor(String selectedNameColor) {
        this.selectedNameColor = selectedNameColor;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getSelectedIconUrl() {
        return selectedIconUrl;
    }

    public void setSelectedIconUrl(String selectedIconUrl) {
        this.selectedIconUrl = selectedIconUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
