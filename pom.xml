<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.corp.bff.service</groupId>
        <artifactId>template-parent-pom</artifactId>
        <version>1.0.28</version>
    </parent>

    <artifactId>basichometrip</artifactId>
    <packaging>war</packaging>
    <version>1.0.0</version>

    <properties>
        <!-- 契约服务接口 比如 com.ctrip.corp.bff.official.backend.contract.CorpBffBasicOfficialBackendService -->
        <baiji.service.define>com.ctrip.corp.bff.basic.home.trip.contract.CorpBffBasicHomeTripService</baiji.service.define>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.soa.corp.booking.corphotelbookqueryservice.v1</groupId>
                <artifactId>corphotelbookqueryservice</artifactId>
                <version>0.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.corp.bff.service</groupId>
                <artifactId>integration-entity</artifactId>
                <version>0.0.42</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.soa.corp.authorize.approvews.v1</groupId>
            <artifactId>corpapproval4jservice</artifactId>
            <version>1.24.11</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.user.corpuserinfo4j.v1</groupId>
            <artifactId>corpuserinfoservice4j</artifactId>
            <version>0.0.383</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.23144</groupId>
            <artifactId>corpauthoritymanageservice</artifactId>
            <version>0.0.17</version>
        </dependency>
        <dependency>
            <groupId>qunar.tc.qschedule</groupId>
            <artifactId>qschedule-client</artifactId>
        </dependency>

        <!-- 本应用契约 -->
        <dependency>
            <groupId>com.ctrip.corp.bff.basic.home.trip</groupId>
            <artifactId>corpbffbasichometripservice</artifactId>
            <version>0.0.20</version>
        </dependency>

        <!-- 首页 集成服务 -->
        <dependency>
            <groupId>com.ctrip.corp.bff.basic.home</groupId>
            <artifactId>corpbffbasichomeservice</artifactId>
            <version>0.0.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>integration-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>basic-integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.corp.user.accountquery.v1</groupId>
            <artifactId>corpuseraccountqueryservice</artifactId>
            <version>0.0.73</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.user.corp4j.v1</groupId>
            <artifactId>corp4jservice</artifactId>
            <version>0.0.115</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.31804</groupId>
            <artifactId>corpbfftools</artifactId>
            <version>0.0.18</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>basic-integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- gateway -->
        <dependency>
            <groupId>com.ctrip.corp.bff.service</groupId>
            <artifactId>template-gateway</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>template-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.idgen</groupId>
            <artifactId>idgen-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Unit tests dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
        </dependency>
        <!-- Unit tests dependencies -->
        <!--- 定义 jacoco 依赖  注意不是在dependencyManagement， dependencyManagement中只是声明-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>6.0.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.booking.corphotelbookqueryservice.v1</groupId>
            <artifactId>corphotelbookqueryservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.corp.settlement.settings.uncore.v1</groupId>
            <artifactId>corpsettlementsettingsuncoresvcapplication</artifactId>
            <version>1.2.25</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.31793</groupId>
            <artifactId>corpbffmcinfoservice</artifactId>
            <version>0.1.37</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>basic-integration-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.32071</groupId>
            <artifactId>corpbffbasicim</artifactId>
            <version>0.0.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <!--离线模式必需指定， 否则到模块根目录而不是target目录了-->
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <argLine>
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.management/java.lang.management=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.access=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.util.random=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                        --add-opens jdk.jfr/jdk.jfr.internal.tool=ALL-UNNAMED
                        --add-opens jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>
            <!--groovy 编译-->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <encoding>${file_encoding}</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/</generatedSourcesDirectory>
                    <annotationProcessors>
                        <annotationProcessor>
                            com.ctrip.corp.bff.framework.template.service.generate.ServiceGenerateAnnotationProcessor
                        </annotationProcessor>
                    </annotationProcessors>
                    <showWarnings>true</showWarnings>
                    <fork>true</fork>
                    <compilerArgs>
                        <arg>-ABaijiServiceDefine=${baiji.service.define}</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
